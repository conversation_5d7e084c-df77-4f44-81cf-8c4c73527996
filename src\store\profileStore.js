import { create } from "zustand";
import axios from "axios";
import { ADDINFO_ENDPOINTS, DELETE_INFO } from "../lib/constants";
import { useUserInfoStore } from "./userInfoStore"; // 👈 import centralized user store

export const useProfileStore = create((set, get) => ({
    profiles: [],
    isLoading: false,
    error: null,

    fetchProfiles: async () => {
        set({ isLoading: true, error: null });
        try {
            const { userInfo } = useUserInfoStore.getState(); // 👈 get user data from global store
            const profiles = userInfo?.Profiles || [];

            set({ profiles, isLoading: false });
        } catch (err) {
            console.error("Error fetching profiles:", err.message);
            set({ error: "Failed to load profiles", isLoading: false });
        }
    },

    addProfile: async (profileData) => {
        try {
            const res = await axios.post(ADDINFO_ENDPOINTS.PROFILES, profileData, {
                withCredentials: true,
            });
            set((state) => ({
                profiles: [...state.profiles, res.data],
            }));
            localStorage.setItem("profilesUpdated", Date.now().toString());
        } catch (error) {
            console.error("Error adding profile:", error);
        }
    },

    deleteProfile: async (profile) => {
        try {
            await axios.delete(DELETE_INFO.PROFILE(profile._id), {
                withCredentials: true,
            });

            set((state) => ({
                profiles: state.profiles.filter((p) => p._id !== profile._id),
            }));
        } catch (error) {
            console.error("Failed to delete profile:", error);
        }
    }
}));
