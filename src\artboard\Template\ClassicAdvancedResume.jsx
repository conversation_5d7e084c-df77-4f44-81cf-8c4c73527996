import React, { useEffect } from "react";
import {
  FiUser,
  FiMail,
  FiPhone,
  FiMapPin,
  FiGlobe,
  FiBriefcase,
  FiBookOpen,
  FiAward,
  FiFileText,
  FiStar,
  FiHeart,
  FiUsers,
  FiCheckCircle,
  FiFolder,
  FiLinkedin,
} from "react-icons/fi";

import { useAuthStore } from "../../store/authStore";
import { useResumeStore } from "../../store/useResumeDetailStore";
import { useThemeStore } from "../../store/themeStore";
import { useTypographyStore } from "../../store/themeTypographyStore";

// 🧠 Section Icon Mapping
const iconMap = {
  Summary: <FiFileText />,
  Skills: <FiCheckCircle />,
  Languages: <FiGlobe />,
  Interests: <FiHeart />,
  Experience: <FiBriefcase />,
  Projects: <FiFolder />,
  Education: <FiBookOpen />,
  Certifications: <FiAward />,
  Awards: <FiStar />,
  Publications: <FiFileText />,
  Volunteering: <FiUsers />,
  References: <FiUser />,
  Profiles: <FiLinkedin />,
};

const ModernTwoColumnTemplate = ({ exportMode = false, themeOverrides = {} }) => {
  const { user, fetchCurrentUser } = useAuthStore();
  const { resume, fetchResume } = useResumeStore();

  const {
    primaryColor: storePrimary,
    backgroundColor: storeBackground,
    textColor: storeText,
  } = useThemeStore();

  const {
    fontFamily,
    fontVariant,
    fontSize,
    lineHeight,
    hideIcons,
    underlineLinks,
  } = useTypographyStore();

  const {
    primaryColor = storePrimary,
    backgroundColor = exportMode ? "#ffffff" : storeBackground,
    textColor = exportMode ? "#29354d" : storeText,
  } = themeOverrides;

  const variantStyle = {};
  if (fontVariant === "italic") variantStyle.fontStyle = "italic";
  else if (!isNaN(fontVariant)) variantStyle.fontWeight = fontVariant;

  useEffect(() => {
    const linkId = "dynamic-google-font";
    let link = document.getElementById(linkId);
    if (link) link.remove();
    link = document.createElement("link");
    link.id = linkId;
    link.rel = "stylesheet";
    link.href = `https://fonts.googleapis.com/css?family=${fontFamily.replace(/ /g, "+")}:${fontVariant}`;
    document.head.appendChild(link);
  }, [fontFamily, fontVariant]);

  useEffect(() => {
    if (!user) fetchCurrentUser();
  }, []);

  useEffect(() => {
    if (user?.resumeId && !resume) fetchResume(user.resumeId);
  }, [user]);

  if (!resume || !user)
    return exportMode ? null : (
      <p className="text-center mt-20 text-gray-500">Loading resume...</p>
    );

  const {
    Email,
    Headline,
    Phone,
    Location,
    Website,
    ProfilePic,
    Profiles = [],
    Experience = [],
    Education = [],
    Skills = [],
    Languages = [],
    Certifications = [],
    Awards = [],
    Projects = [],
    Publications = [],
    Volunteering = [],
    References = [],
    Interests = [],
    summery = "",
  } = resume;

  const { name } = user;

  return (
    <div
      className="a4-container px-10 py-6 grid grid-cols-4 gap-6"
      style={{
        backgroundColor,
        color: textColor,
        fontFamily,
        fontSize,
        lineHeight,
        ...variantStyle,
      }}
    >
      {/* Sidebar */}
      <aside className="col-span-1 space-y-4">
        {ProfilePic && (
          <img
            src={ProfilePic}
            alt="Profile"
            className="w-full h-48 rounded-md object-cover shadow"
          />
        )}
        <div className="text-center">
          <h1 className="text-xl font-bold mt-3" style={{ color: primaryColor }}>
            {!hideIcons && <FiUser className="inline mr-1" />}
            {name}
          </h1>
          {Headline && <p className="italic text-xs text-gray-600">{Headline}</p>}
        </div>

        <div className="text-xs space-y-1 mt-4">
          {Email && <p>{!hideIcons && <FiMail className="inline mr-1" />}{Email}</p>}
          {Phone && <p>{!hideIcons && <FiPhone className="inline mr-1" />}{Phone}</p>}
          {Location && <p>{!hideIcons && <FiMapPin className="inline mr-1" />}{Location}</p>}
          {Website && (
            <p>
              {!hideIcons && <FiGlobe className="inline mr-1" />}
              <a
                href={Website}
                style={{
                  textDecoration: underlineLinks ? "underline" : "none",
                  color: primaryColor,
                }}
              >
                {Website}
              </a>
            </p>
          )}
        </div>

        <RenderListSection title="Skills" items={Skills.map(s => s.Skill)} primaryColor={primaryColor} hideIcons={hideIcons} />
        <RenderListSection title="Languages" items={Languages.map(l => `${l.Name} - ${l.Proficiency}`)} primaryColor={primaryColor} hideIcons={hideIcons} />
        <RenderListSection title="Interests" items={Interests.map(i => i.Interest || i)} primaryColor={primaryColor} hideIcons={hideIcons} />
      </aside>

      {/* Main Content */}
      <main className="col-span-3 space-y-6">
        {summery && (
          <Section title="Summary" primaryColor={primaryColor} hideIcons={hideIcons}>
            <p>{summery}</p>
          </Section>
        )}

        <RenderExperience title="Experience" items={Experience} primaryColor={primaryColor} hideIcons={hideIcons} />
        <RenderExperience title="Projects" items={Projects} primaryColor={primaryColor} isProject hideIcons={hideIcons} />
        <RenderExperience title="Education" items={Education} primaryColor={primaryColor} isEducation hideIcons={hideIcons} />
        <RenderExperience title="Certifications" items={Certifications} primaryColor={primaryColor} isCert hideIcons={hideIcons} />
        <RenderExperience title="Awards" items={Awards} primaryColor={primaryColor} isAward hideIcons={hideIcons} />
        <RenderExperience title="Publications" items={Publications} primaryColor={primaryColor} isPub hideIcons={hideIcons} />
        <RenderExperience title="Volunteering" items={Volunteering} primaryColor={primaryColor} isVolunteer hideIcons={hideIcons} />
        <RenderExperience title="References" items={References} primaryColor={primaryColor} isRef hideIcons={hideIcons} />

        {Profiles.length > 0 && (
          <Section title="Profiles" primaryColor={primaryColor} hideIcons={hideIcons}>
            <ul className="list-disc pl-4 text-xs">
              {Profiles.map((p, idx) => (
                <li key={p._id || idx}>
                  {p.Network}: <a
                    href={p.ProfileLink}
                    style={{
                      textDecoration: underlineLinks ? "underline" : "none",
                      color: primaryColor,
                    }}
                    target="_blank"
                    rel="noreferrer"
                  >{p.Username}</a>
                </li>
              ))}
            </ul>
          </Section>
        )}
      </main>
    </div>
  );
};

// Section component with hideIcons support
const Section = ({ title, children, primaryColor, hideIcons }) => (
  <div className="mb-4">
    <h2
      className="font-bold text-sm border-b pb-1 uppercase tracking-wide flex items-center gap-2"
      style={{ borderColor: primaryColor, borderBottomWidth: 2 }}
    >
      {!hideIcons && iconMap[title] && (
        <span className="text-base">{iconMap[title]}</span>
      )}
      {title}
    </h2>
    {children}
  </div>
);

// List-style sections (Skills, Interests, Languages)
const RenderListSection = ({ title, items, primaryColor, hideIcons }) =>
  items.length > 0 && (
    <Section title={title} primaryColor={primaryColor} hideIcons={hideIcons}>
      <ul className="list-disc pl-4 text-xs space-y-1">
        {items.map((item, idx) => <li key={idx}>{item}</li>)}
      </ul>
    </Section>
  );

// Experience, Projects, etc.
const RenderExperience = ({
  title,
  items,
  primaryColor,
  hideIcons,
  isProject,
  isEducation,
  isCert,
  isAward,
  isPub,
  isVolunteer,
  isRef,
}) =>
  items.length > 0 && (
    <Section title={title} primaryColor={primaryColor} hideIcons={hideIcons}>
      {items.map((item, idx) => (
        <Item
          key={item._id || idx}
          title={
            isEducation ? `${item.Degree} at ${item.Institution}` :
            isCert ? item.Title :
            isAward ? item.Title :
            isPub ? item.Title :
            isVolunteer ? `${item.Position} at ${item.Organization}` :
            isRef ? item.Name :
            isProject ? item.Title :
            `${item.Position} at ${item.Company}`
          }
          subtitle={
            isEducation || isVolunteer
              ? `${formatDate(item.StartDate)} - ${formatDate(item.EndDate)} | ${item.Location}`
              : isCert || isAward
              ? `${item.Issuer} - ${formatDate(item.Date)}`
              : isPub
              ? `${item.Publisher} - ${formatDate(item.Date)}`
              : isRef
              ? `${item.Position} at ${item.Company}`
              : item.Link || item.Location
          }
          description={item.Description}
          extra={
            isProject ? item.Technologies?.join(", ") :
            isPub ? item.Website :
            isRef ? `${item.Email} | ${item.Phone}` :
            null
          }
        />
      ))}
    </Section>
  );

const Item = ({ title, subtitle, description, extra }) => (
  <div className="mb-2">
    <p className="text-sm font-semibold">{title}</p>
    {subtitle && <p className="text-xs italic">{subtitle}</p>}
    {description && <p className="text-xs">{description}</p>}
    {extra && <p className="text-xs text-gray-500">{extra}</p>}
  </div>
);

const formatDate = (date) => {
  if (!date) return "Present";
  try {
    return new Date(date).toLocaleDateString("en-IN", {
      month: "short",
      year: "numeric",
    });
  } catch {
    return "N/A";
  }
};

export default ModernTwoColumnTemplate;
