import React, { useRef, useEffect, useState } from "react";
import ManagementBar from "../artboard/artboardRightsidebar/ManagementBar";
import MenuLayout from "../artboard/artBoardleftsidebar/MenuBar";
import { useZoom } from "../hooks/useZoom";
import { useTemplateStore } from "../store/templateStore";
import { TEMPLATES } from "../artboard/Template";
import { FaMinus, FaPlus } from "react-icons/fa";

const ResumeEditor = () => {
  const { zoom, setZoom, increaseZoom, decreaseZoom, resetZoom } = useZoom();
  const { selectedTemplateId } = useTemplateStore();
  const defaultTemplateKey = "ganesh";

  const containerRef = useRef(null);
  const lastTouchDistance = useRef(null);

  const SelectedTemplate =
    TEMPLATES[selectedTemplateId]?.component ||
    TEMPLATES[defaultTemplateKey]?.component;

  const templateKey = selectedTemplateId || defaultTemplateKey;

  const [position, setPosition] = useState({ x: 0, y: 0 });
  const dragRef = useRef({ isDragging: false, startX: 0, startY: 0 });

  useEffect(() => {
    const storedZoom = localStorage.getItem(`zoom_${templateKey}`);
    if (storedZoom) setZoom(parseFloat(storedZoom));
  }, [templateKey, setZoom]);

  const handleTouchMove = (e) => {
    if (e.touches.length === 2) {
      const [touch1, touch2] = e.touches;
      const dx = touch2.clientX - touch1.clientX;
      const dy = touch2.clientY - touch1.clientY;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (lastTouchDistance.current) {
        const delta = distance - lastTouchDistance.current;
        if (Math.abs(delta) > 5) {
          setZoom((z) => {
            const next =
              delta > 0 ? Math.min(z + 0.02, 2) : Math.max(z - 0.02, 0.5);
            localStorage.setItem(`zoom_${templateKey}`, next.toFixed(2));
            return next;
          });
        }
      }
      lastTouchDistance.current = distance;
    }
  };

  const handleTouchEnd = () => {
    lastTouchDistance.current = null;
  };

  const handleMouseDown = (e) => {
    dragRef.current.isDragging = true;
    dragRef.current.startX = e.clientX - position.x;
    dragRef.current.startY = e.clientY - position.y;
  };

  const handleMouseMove = (e) => {
    if (dragRef.current.isDragging) {
      const x = e.clientX - dragRef.current.startX;
      const y = e.clientY - dragRef.current.startY;
      setPosition({ x, y });
    }
  };

  const handleMouseUp = () => {
    dragRef.current.isDragging = false;
  };

  useEffect(() => {
    const el = containerRef.current;
    if (!el) return;

    el.addEventListener("touchmove", handleTouchMove, { passive: false });
    el.addEventListener("touchend", handleTouchEnd);

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);

    return () => {
      el.removeEventListener("touchmove", handleTouchMove);
      el.removeEventListener("touchend", handleTouchEnd);

      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, []);

  const handleZoom = (newZoom) => {
    setZoom(newZoom);
    localStorage.setItem(`zoom_${templateKey}`, newZoom.toFixed(2));
  };

  return (
    <div
      ref={containerRef}
      className="flex h-screen w-screen overflow-hidden bg-[#17181c] text-white"
    >
      {/* Left Sidebar */}
      <aside className="w-[360px] min-w-[300px] max-w-[400px] border-r border-[#232428] bg-[#17181c] overflow-hidden">
        <MenuLayout />
      </aside>

      {/* Resume Canvas */}
      <main className="flex-1 relative overflow-auto bg-[#131313] min-w-0">
        <div
          onMouseDown={handleMouseDown}
          className="absolute left-1/2 top-1/2"
          style={{
            transform: `translate(-50%, -50%) translate(${position.x}px, ${position.y}px) scale(${zoom})`,
            transformOrigin: "center center",
            minWidth: 860,
          }}
        >
          {SelectedTemplate ? (
            <SelectedTemplate />
          ) : (
            <div className="text-white text-center">Template not found.</div>
          )}
        </div>

        {/* Sticky Zoom Controls */}
        <div className="fixed bottom-6 left-1/2 -translate-x-1/2 z-30 flex gap-3">
          <button
            onClick={() => handleZoom(Math.max(zoom - 0.1, 0.5))}
            className="bg-[#222] border border-[#333] p-2 rounded-lg hover:bg-[#333] transition"
            title="Zoom Out"
          >
            <FaMinus />
          </button>
          <button
            onClick={() => handleZoom(1)}
            className="bg-[#222] border border-[#333] p-2 rounded-lg hover:bg-[#333] transition"
            title="Reset Zoom"
          >
            ♻️
          
          </button>
          <button
            onClick={() => handleZoom(Math.min(zoom + 0.1, 2))}
            className="bg-[#222] border border-[#333] p-2 rounded-lg hover:bg-[#333] transition"
            title="Zoom In"
          >
            <FaPlus />
          </button>
        </div>
      </main>

      {/* Sticky Right Sidebar */}
      <aside className="w-[380px] min-w-[300px] max-w-[440px] border-l border-[#232428] bg-[#17181c] overflow-y-auto">
        <div className="sticky top-0 z-20 bg-[#17181c]">
          <ManagementBar />
        </div>
      </aside>
    </div>
  );
};

export default ResumeEditor;
