// store/userStore.js
import { create } from "zustand";
import axios from "axios";
import { GETINFO_ENDPOINT } from "../lib/constants";

export const useUserStore = create((set) => ({
    user: null,
    isLoading: false,
    error: null,

    fetchUser: async () => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.get(GETINFO_ENDPOINT, {
                withCredentials: true,
            });
            set({ user: res.data.user, isLoading: false });
        } catch (err) {
            set({
                error: err.response?.data?.error || "Could not fetch user data",
                isLoading: false,
            });
        }
    },
    updateUser: (updatedUser) => set({ user: updatedUser }),
    clearUser: () => set({ user: null, error: null }),
}));
