import React, { useEffect, useState } from "react";
import { Combobox } from "@headlessui/react";
import { CheckIcon, ChevronUpDownIcon } from "@heroicons/react/20/solid";
import { useTypographyStore } from "../../../store/themeTypographyStore";

const GOOGLE_FONTS_API =
  "https://www.googleapis.com/webfonts/v1/webfonts?key=AIzaSyAmsbVKyhkl1-Q4GfNnJrxovldJ9pW2MrM";

const defaultSubsets = ["latin", "latin-ext", "cyrillic"];

const Typography = () => {
  const [groupedFonts, setGroupedFonts] = useState({});
  const [fontVariants, setFontVariants] = useState(["regular"]);
  const [fontSubset, setFontSubset] = useState("latin");
  const [query, setQuery] = useState("");

  const {
    fontFamily,
    fontVariant,
    fontSize,
    lineHeight,
    hideIcons,
    underlineLinks,
    setFontFamily,
    setFontVariant,
    setFontSize,
    setLineHeight,
    setHideIcons,
    setUnderlineLinks,
    resetTypography,
  } = useTypographyStore();

  const fontsFlat = Object.values(groupedFonts).flat();

  const filteredFonts =
    query === ""
      ? fontsFlat
      : fontsFlat.filter((font) =>
        font.family.toLowerCase().includes(query.toLowerCase())
      );

  // Fetch fonts and group by first letter (5 max per letter)
  useEffect(() => {
    const fetchFonts = async () => {
      try {
        const res = await fetch(GOOGLE_FONTS_API);
        const data = await res.json();
        const grouped = {};

        data.items.forEach((font) => {
          const firstLetter = font.family.charAt(0).toUpperCase();
          if (!grouped[firstLetter]) grouped[firstLetter] = [];
          if (grouped[firstLetter].length < 5) grouped[firstLetter].push(font);
        });

        // Load top 100 preview fonts dynamically
        const link = document.createElement("link");
        link.rel = "stylesheet";
        link.href =
          "https://fonts.googleapis.com/css2?" +
          Object.values(grouped)
            .flat()
            .map((font) => `family=${font.family.replace(/ /g, "+")}`)
            .join("&") + "&display=swap";
        document.head.appendChild(link);

        setGroupedFonts(grouped);
      } catch (error) {
        console.error("Failed to load fonts:", error);
      }
    };

    fetchFonts();
  }, []);

  // Load selected font for preview
  useEffect(() => {
    if (!fontFamily || !fontVariant) return;

    const linkId = "dynamic-google-font";
    let link = document.getElementById(linkId);
    if (link) link.remove();

    const formattedVariant = fontVariant.replace("italic", "i").replace(/\D/g, "");
    const variant = fontVariant.includes("italic")
      ? `${formattedVariant}italic`
      : fontVariant;

    link = document.createElement("link");
    link.id = linkId;
    link.rel = "stylesheet";
    link.href = `https://fonts.googleapis.com/css2?family=${fontFamily.replace(
      / /g,
      "+"
    )}:wght@${variant}&display=swap`;
    document.head.appendChild(link);
  }, [fontFamily, fontVariant]);

  // Update font variants on font change
  useEffect(() => {
    const found = fontsFlat.find((f) => f.family === fontFamily);
    if (found) {
      setFontVariants(found.variants || ["regular"]);
      setFontVariant("regular");
    }
  }, [fontFamily, groupedFonts]);

  return (
    <div className="p-4 bg-white rounded-xl shadow-md max-w-sm space-y-4">
      <h2 className="text-lg font-bold text-[#29354d]">Typography Settings</h2>

      {/* Font Family with Preview */}
      <div>
        <label className="block font-semibold text-sm text-gray-700">
          Font Family
        </label>
        <Combobox value={fontFamily} onChange={setFontFamily}>
          <div className="relative mt-1">
            <div className="relative w-full cursor-default overflow-hidden rounded-lg bg-white text-left border">
              <Combobox.Input
                className="w-full border-none py-2 pl-3 pr-10 text-sm leading-5 text-gray-900 focus:ring-0"
                displayValue={(font) => font}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Search font..."
              />
              <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
                <ChevronUpDownIcon className="h-5 w-5 text-gray-400" />
              </Combobox.Button>
            </div>
            {filteredFonts.length > 0 && (
              <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-sm shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                {filteredFonts.map((font) => (
                  <Combobox.Option
                    key={font.family}
                    className={({ active }) =>
                      `relative cursor-default select-none py-2 pl-10 pr-4 ${active ? "bg-blue-600 text-white" : "text-gray-900"
                      }`
                    }
                    value={font.family}
                  >
                    {({ selected, active }) => (
                      <>
                        <span
                          className={`block truncate ${selected ? "font-semibold" : "font-normal"
                            }`}
                          style={{ fontFamily: font.family }}
                        >
                          {font.family}
                        </span>
                        {selected && (
                          <span
                            className={`absolute inset-y-0 left-0 flex items-center pl-3 ${active ? "text-white" : "text-blue-600"
                              }`}
                          >
                            <CheckIcon className="h-5 w-5" />
                          </span>
                        )}
                      </>
                    )}
                  </Combobox.Option>
                ))}
              </Combobox.Options>
            )}
          </div>
        </Combobox>
      </div>

      {/* Font Variant + Subset */}
      <div className="flex gap-3">
        <div className="flex-1">
          <label className="block font-semibold text-sm text-gray-700">
            Font Subset
          </label>
          <select
            value={fontSubset}
            onChange={(e) => setFontSubset(e.target.value)}
            className="w-full border rounded px-2 py-1 mt-1"
          >
            {defaultSubsets.map((sub) => (
              <option key={sub} value={sub}>
                {sub.charAt(0).toUpperCase() + sub.slice(1)}
              </option>
            ))}
          </select>
        </div>
        <div className="flex-1">
          <label className="block font-semibold text-sm text-gray-700">
            Font Variant
          </label>
          <select
            value={fontVariant}
            onChange={(e) => setFontVariant(e.target.value)}
            className="w-full border rounded px-2 py-1 mt-1"
          >
            {fontVariants.map((variant) => (
              <option key={variant} value={variant}>
                {variant}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Font Size */}
      <div>
        <label className="block font-semibold text-sm text-gray-700">
          Font Size <span className="text-gray-500">({fontSize}px)</span>
        </label>
        <input
          type="range"
          min={10}
          max={48}
          value={fontSize}
          onChange={(e) => setFontSize(Number(e.target.value))}
          className="w-full mt-1"
        />
      </div>

      {/* Line Height */}
      <div>
        <label className="block font-semibold text-sm text-gray-700">
          Line Height <span className="text-gray-500">({lineHeight})</span>
        </label>
        <input
          type="range"
          min={1}
          max={2.5}
          step={0.05}
          value={lineHeight}
          onChange={(e) => setLineHeight(Number(e.target.value))}
          className="w-full mt-1"
        />
      </div>

      {/* Toggles */}
      <div className="flex items-center">
        <input
          type="checkbox"
          id="hide-icons"
          checked={hideIcons}
          onChange={(e) => setHideIcons(e.target.checked)}
          className="mr-2"
        />
        <label htmlFor="hide-icons" className="text-sm">
          Hide Icons
        </label>
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="underline-links"
          checked={underlineLinks}
          onChange={(e) => setUnderlineLinks(e.target.checked)}
          className="mr-2"
        />
        <label htmlFor="underline-links" className="text-sm">
          Underline Links
        </label>
      </div>

      {/* Reset Button */}
      <button
        onClick={resetTypography}
        className="w-full text-sm font-semibold text-gray-700 bg-gray-100 hover:bg-gray-200 py-1.5 rounded border"
      >
        Reset to Default
      </button>

      {/* Live Preview */}
      <div
        className="rounded-lg p-3 border mt-3"
        style={{
          fontFamily,
          fontSize,
          lineHeight,
        }}
      >
        <span style={{ display: hideIcons ? "none" : "inline" }}>🔤 </span>
        The quick brown fox jumps over the lazy dog.{" "}
        <a
          href="#"
          style={{
            textDecoration: underlineLinks ? "underline" : "none",
            color: "#1976d2",
          }}
        >
          Example Link
        </a>
      </div>
    </div>
  );
};

export default Typography;
