import React from "react";

const Header = ({
  name,
  headline,
  contactInfo = {},
  socialLinks = [],
  containerStyle = "",
  nameStyle = "",
  headlineStyle = "",
  contactStyle = "",
  linkStyle = "",
}) => {
  return (
    <div className={`text-center space-y-2 mb-6 ${containerStyle}`}>
      {/* Name */}
      {name && <h1 className={`text-3xl font-bold ${nameStyle}`}>{name}</h1>}

      {/* Headline */}
      {headline && (
        <h2 className={`text-sm font-medium text-gray-700 uppercase ${headlineStyle}`}>
          {headline}
        </h2>
      )}

      {/* Contact Info */}
      <div className={`flex flex-wrap justify-center gap-2 text-sm ${contactStyle}`}>
        {contactInfo.phone && <span>📞 {contactInfo.phone}</span>}
        {contactInfo.email && (
          <span>
            ✉️{" "}
            <a href={`mailto:${contactInfo.email}`} className="hover:underline">
              {contactInfo.email}
            </a>
          </span>
        )}
        {contactInfo.address && <span>📍 {contactInfo.address}</span>}
        {contactInfo.website && (
          <span>
            <a
              href={contactInfo.website}
              className={`text-blue-600 hover:underline ${linkStyle}`}
              target="_blank"
              rel="noreferrer"
            >
              🌐 {contactInfo.website}
            </a>
          </span>
        )}
      </div>

      {/* Social Links */}
      {socialLinks.length > 0 && (
        <div className={`flex justify-center gap-4 text-sm flex-wrap ${contactStyle}`}>
          {socialLinks.map((item, idx) => (
            <a
              key={idx}
              href={item.url}
              className={`hover:underline ${linkStyle}`}
              target="_blank"
              rel="noreferrer"
            >
              {item.label}
            </a>
          ))}
        </div>
      )}
    </div>
  );
};

export default Header;
