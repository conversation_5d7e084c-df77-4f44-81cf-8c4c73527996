import { create } from "zustand";
import axios from "axios";
import { ADDINFO_ENDPOINTS, DELETE_INFO, GETINFO_ENDPOINT } from "../lib/constants";

export const useProjectStore = create((set) => ({
    projects: [],
    isLoading: false,
    error: null,

    fetchProjects: async () => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.get(GETINFO_ENDPOINT, { withCredentials: true });
            const user = res.data.user;
            set({
                projects: user?.Project || [],
                isLoading: false,
            });
        } catch (error) {
            console.error("Failed to fetch projects:", error);
            set({ isLoading: false, error: "Failed to fetch projects" });
        }
    },

    addProject: async (data) => {
        try {
            const res = await axios.post(ADDINFO_ENDPOINTS.PROJECT, data, {
                withCredentials: true,
            });

            set((state) => ({
                projects: [...state.projects, res.data], // Assuming API returns the newly added project
            }));

            await useProjectStore.getState().fetchProjects();
        } catch (error) {
            console.error("Failed to add project:", error);
        }
    },
    deleteProject: async (id) => {
        try {
            await axios.delete(DELETE_INFO.PROJECT(id), {
                withCredentials: true,
            });

            set((state) => ({
                projects: state.projects.filter((project) => project._id !== id),
            }));

            await useProjectStore.getState().fetchProjects();
        } catch (error) {
            console.error("Failed to delete project:", error);
        }
    },
    updateProject: async (id, data) => {
        try {
            const res = await axios.put(`${ADDINFO_ENDPOINTS.PROJECT}/${id}`, data, {
                withCredentials: true,
            });

            set((state) => ({
                projects: state.projects.map((project) =>
                    project._id === id ? res.data : project
                ),
            }));
            await useProjectStore.getState().fetchProjects();
        } catch (error) {
            console.error("Failed to update project:", error);
        }
    },
}));
