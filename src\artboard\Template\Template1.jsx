// Template1.jsx
import React from "react";

const Template1 = ({ data }) => {
  const defaultData = {
    header: {
      name: "<PERSON>",
      title: "Medical Virtual Assistant",
      phone: "+************",
      email: "<EMAIL>",
      address: "123 Anywhere St., Any City",
      website: "www.reallygreatsite.com",
      image: "https://images.unsplash.com/photo-1607746882042-944635dfe10e"
    },
    summary:
      "Experienced and dedicated Medical Virtual Assistant with a proven track record of providing exceptional administrative support to healthcare professionals.",
    skills: [
      "Patient Records",
      "Appointment Scheduling",
      "Medical Terminology",
      "Confidential Handling"
    ],
    languages: ["English", "Spanish"],
    experience: [
      {
        company: "Salford & Co",
        role: "Medical Virtual Assistant",
        period: "2020 - Today",
        duties: [
          "Schedule appointments and manage calendars.",
          "Manage electronic health records (EHR)."
        ],
      },
      {
        company: "Ingood Company",
        role: "Medical Virtual Assistant",
        period: "2022 - 2023",
        duties: [
          "Handle patient inquiries.",
          "Perform data entry of billing and insurance."
        ],
      },
      {
        company: "Keithston & Partner",
        role: "Medical Virtual Assistant",
        period: "2020 - 2021",
        duties: [
          "Coordinate telemedicine appointments.",
          "Assist physicians with documentation."
        ],
      }
    ],
    education: [
      {
        degree: "Bachelor of Science in Nursing",
        institution: "Really Great University · 2015–2020",
      },
    ],
    certifications: [
      {
        name: "Basic Life Support (BLS) Certification",
        issuer: "American Heart Association"
      },
      {
        name: "HIPAA Training",
        issuer: "Online Medical Institute"
      },
      {
        name: "Infection Control Certification",
        issuer: "Healthcare Board"
      }
    ],
    volunteering: [
      {
        organization: "Health Awareness Camp",
        role: "Volunteer Coordinator",
        period: "2019 – Present",
        details: [
          "Organized health awareness sessions for rural communities.",
          "Coordinated with medical staff for logistics."
        ]
      }
    ],
    references: [
      {
        name: "Hannah Morales",
        contact: "+************",
        relation: "Founder | Liorcia & Co."
      }
    ]
  };

  const resumeData = data || defaultData;

  return (
    <div className="bg-white w-[210mm] h-[297mm] mx-auto shadow-xl p-[20mm] print:p-[20mm] print:shadow-none text-gray-800">
      {/* Header */}
      <div className="flex justify-between items-start border-b-4 border-gray-800 pb-4 bg-[#4b2c2c] text-white rounded-md px-6">
        <div>
          <h1 className="text-3xl font-extrabold tracking-wide" contentEditable>{resumeData.header.name}</h1>
          <h2 className="text-sm text-gray-200 mt-1" contentEditable>{resumeData.header.title}</h2>
        </div>
        <div className="text-xs leading-5 space-y-1 text-right" contentEditable>
          <p>📞 {resumeData.header.phone}</p>
          <p>📧 {resumeData.header.email}</p>
          <p>📍 {resumeData.header.address}</p>
          <p>🌐 {resumeData.header.website}</p>
        </div>
      </div>

      <div className="flex mt-8 gap-10">
        {/* Left Column */}
        <div className="w-1/2">
          <div className="w-48 h-48 border-2 border-[#4b2c2c] overflow-hidden rounded-lg mb-6 cursor-pointer">
            <img src={resumeData.header.image} alt="Profile" className="w-full h-full object-cover" />
          </div>

          {/* Summary */}
          <section className="mb-6">
            <h2 className="uppercase text-sm font-bold bg-black text-white inline-block px-2 py-1 rounded" contentEditable>Summary</h2>
            <p className="text-sm text-gray-700 mt-2 leading-relaxed" contentEditable>{resumeData.summary}</p>
          </section>

          {/* Skills */}
          <section className="mb-6">
            <h2 className="uppercase text-sm font-bold bg-black text-white inline-block px-2 py-1 rounded" contentEditable>Skills</h2>
            <ul className="list-disc pl-5 text-sm text-gray-700 mt-2">
              {resumeData.skills.map((skill, index) => (
                <li key={index} contentEditable>{skill}</li>
              ))}
            </ul>
          </section>

          {/* Languages */}
          <section className="mb-6">
            <h2 className="uppercase text-sm font-bold bg-black text-white inline-block px-2 py-1 rounded" contentEditable>Languages</h2>
            <ul className="list-disc pl-5 text-sm text-gray-700 mt-2">
              {resumeData.languages.map((lang, index) => (
                <li key={index} contentEditable>{lang}</li>
              ))}
            </ul>
          </section>

          {/* Education */}
          <section className="mb-6">
            <h2 className="uppercase text-sm font-bold bg-black text-white inline-block px-2 py-1 rounded" contentEditable>Education</h2>
            {resumeData.education.map((edu, index) => (
              <div key={index} className="text-sm mt-2" contentEditable>
                <p className="font-semibold">{edu.degree}</p>
                <p className="text-gray-600">{edu.institution}</p>
              </div>
            ))}
          </section>

          {/* References */}
          <section>
            <h2 className="uppercase text-sm font-bold bg-black text-white inline-block px-2 py-1 rounded" contentEditable>References</h2>
            {resumeData.references.map((ref, index) => (
              <div key={index} className="text-sm mt-2" contentEditable>
                <p className="font-semibold">{ref.name}</p>
                <p>{ref.contact}</p>
                <p className="text-gray-600 italic">{ref.relation}</p>
              </div>
            ))}
          </section>
        </div>

        {/* Right Column */}
        <div className="w-1/2">
          {/* Experience */}
          <section className="mb-6">
            <h2 className="uppercase text-sm font-bold bg-black text-white inline-block px-2 py-1 rounded" contentEditable>Work Experience</h2>
            {resumeData.experience.map((job, index) => (
              <div key={index} className="mt-3" contentEditable>
                <p className="text-base font-semibold">{job.company}</p>
                <p className="text-sm text-gray-600">{job.role} · {job.period}</p>
                <ul className="list-disc pl-5 text-sm text-gray-700 mt-1">
                  {job.duties.map((duty, idx) => (
                    <li key={idx}>{duty}</li>
                  ))}
                </ul>
              </div>
            ))}
          </section>

          {/* Volunteering */}
          <section className="mb-6">
            <h2 className="uppercase text-sm font-bold bg-black text-white inline-block px-2 py-1 rounded" contentEditable>Volunteering</h2>
            {resumeData.volunteering.map((item, index) => (
              <div key={index} className="mt-3" contentEditable>
                <p className="text-base font-semibold">{item.organization}</p>
                <p className="text-sm text-gray-600">{item.role} · {item.period}</p>
                <ul className="list-disc pl-5 text-sm text-gray-700 mt-1">
                  {item.details.map((line, idx) => <li key={idx}>{line}</li>)}
                </ul>
              </div>
            ))}
          </section>

          {/* Certifications */}
          <section>
            <h2 className="uppercase text-sm font-bold bg-black text-white inline-block px-2 py-1 rounded" contentEditable>Certifications</h2>
            {resumeData.certifications.map((cert, index) => (
              <div key={index} className="text-sm mt-2" contentEditable>
                <p className="font-semibold">{cert.name}</p>
                <p className="text-gray-600">{cert.issuer}</p>
              </div>
            ))}
          </section>
        </div>
      </div>
    </div>
  );
};

export default Template1;
