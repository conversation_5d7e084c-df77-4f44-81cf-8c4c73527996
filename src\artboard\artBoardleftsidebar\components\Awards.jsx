import React, { useState, useEffect } from "react";
import { FaA<PERSON>, Fa<PERSON><PERSON>, FaPlus, FaTimes } from "react-icons/fa";
import { useResumeStore } from "../../../store/useResumeDetailStore";
import { useAddInfoStore } from "../../../store/addInfoStore";
import { useDeleteInfoStore } from "../../../store/deleteInfoStore";
import { useUpdateInfoStore } from "../../../store/updateInfoStore";
import DataCard from "../../Components/DataCard";
import toast from "react-hot-toast";

const Awards = () => {
  const { resume, fetchResume } = useResumeStore();
  const { addInfo, loading: adding } = useAddInfoStore();
  const { deleteInfo } = useDeleteInfoStore();
  const { updateInfo, loading: updating } = useUpdateInfoStore();

  const resumeId = resume?._id;
  const awards = resume?.Awards || [];

  const [showModal, setShowModal] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(null);
  const [formData, setFormData] = useState({
    title: "",
    date: "",
    awarder: "",
    summary: "",
  });

  useEffect(() => {
    if (resumeId) fetchResume(resumeId);
  }, [resumeId]);

  const resetForm = () => {
    setFormData({ title: "", date: "", awarder: "", summary: "" });
    setEditingId(null);
  };

  const openModal = (award = null) => {
    if (award) {
      setFormData({
        title: award.Title,
        date: award.Date?.split("T")[0] || "",
        awarder: award.Issuer,
        summary: award.Description,
      });
      setEditingId(award._id);
    } else {
      resetForm();
    }
    setShowModal(true);
  };

  const closeModal = () => {
    resetForm();
    setShowModal(false);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCreateOrUpdate = async () => {
    const { title, awarder, date, summary } = formData;

    if (!title || !awarder || !date) {
      toast.error("Please fill in all required fields.");
      return;
    }

    const payload = {
      Title: title,
      Issuer: awarder,
      Date: date,
      Description: summary,
    };

    try {
      if (editingId) {
        await updateInfo({
          resumeId,
          section: "awards",
          entryId: editingId,
          updatedData: payload,
        });
        toast.success("Award updated successfully!");
      } else {
        await addInfo({
          resumeId,
          section: "awards",
          newData: payload,
        });
        toast.success("Award added successfully!");
      }

      await fetchResume(resumeId);
      closeModal();
    } catch (err) {
      toast.error("Something went wrong.");
    }
  };

  const confirmDeleteAward = async () => {
    if (!resumeId || !confirmDelete) return;

    try {
      await deleteInfo({
        resumeId,
        section: "awards",
        entryId: confirmDelete._id,
      });
      await fetchResume(resumeId);
      toast.success("Award deleted.");
    } catch (err) {
      toast.error("Failed to delete award.");
    } finally {
      setConfirmDelete(null);
    }
  };

  return (
    <div className="bg-white min-h-screen py-10 px-4 md:px-8 text-[#29354d] font-sans">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <FaAward className="text-[#29354d]" size={22} />
          <h2 className="text-2xl font-extrabold">Awards</h2>
        </div>
        <FaBars size={20} className="text-[#29354d]" />
      </div>

      {/* Add Button */}
      <div
        onClick={() => openModal()}
        className="cursor-pointer border-2 border-dashed border-gray-400 rounded-lg py-5 px-8 flex items-center justify-center bg-gray-200 hover:bg-gray-300 text-[#29354d] text-lg font-medium mb-8"
      >
        <FaPlus className="mr-2" />
        Add a new award
      </div>

      {/* Awards List */}
      <div className="flex flex-wrap gap-4">
        {awards.length === 0 ? (
          <p className="text-gray-500">No awards added yet.</p>
        ) : (
          awards.map((award) => (
            <div key={award._id} className="w-fit min-w-[250px]">
              <DataCard
                title={award.Title}
                subtitle={`${award.Issuer} • ${
                  award.Date
                    ? new Date(award.Date).toLocaleDateString("default", {
                        month: "short",
                        year: "numeric",
                      })
                    : ""
                }`}
                description={award.Description || "No description provided."}
                onEdit={() => openModal(award)}
                onDelete={() => setConfirmDelete(award)}
                data={award}
              />
            </div>
          ))
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center px-4">
          <div className="relative w-full max-w-xl bg-white rounded-2xl p-6 shadow-xl">
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 text-[#29354d] hover:text-black transition"
            >
              <FaTimes size={20} />
            </button>

            <h2 className="text-xl font-bold mb-6 text-[#29354d] flex items-center gap-2">
              <FaPlus /> {editingId ? "Update Award" : "Add New Award"}
            </h2>

            <form onSubmit={(e) => e.preventDefault()} className="space-y-4">
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Award Title"
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              />
              <input
                type="date"
                name="date"
                value={formData.date}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              />
              <input
                type="text"
                name="awarder"
                value={formData.awarder}
                onChange={handleInputChange}
                placeholder="Awarding Organization"
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              />
              <textarea
                name="summary"
                value={formData.summary}
                onChange={handleInputChange}
                placeholder="Describe the award..."
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm resize-vertical min-h-[80px]"
              />
              <div className="flex justify-end">
                <button
                  onClick={handleCreateOrUpdate}
                  disabled={adding || updating}
                  className={`${
                    adding || updating ? "opacity-60 cursor-not-allowed" : ""
                  } bg-[#5f5d59] hover:bg-[#010101] text-[#f8fbff] font-semibold px-6 py-2 rounded-md transition`}
                >
                  {adding || updating
                    ? "Saving..."
                    : editingId
                    ? "Update"
                    : "Create"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Card */}
      {confirmDelete && (
        <div className="fixed inset-0 z-50 bg-black/60 flex items-center justify-center px-4">
          <div className="bg-white max-w-md w-full p-6 rounded-lg shadow-xl text-[#29354d]">
            <h3 className="text-lg font-bold mb-2">Delete Award</h3>
            <p className="text-sm mb-4">
              Are you sure you want to delete{" "}
              <strong>{confirmDelete.Title}</strong>?
            </p>

            <div className="border p-3 rounded bg-gray-50 text-sm mb-4">
              <p>
                <strong>Issuer:</strong> {confirmDelete.Issuer}
              </p>
              <p>
                <strong>Date:</strong>{" "}
                {new Date(confirmDelete.Date).toLocaleDateString()}
              </p>
              {confirmDelete.Description && (
                <p className="mt-2">
                  <strong>Description:</strong> {confirmDelete.Description}
                </p>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setConfirmDelete(null)}
                className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-sm"
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteAward}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 text-sm"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Awards;
