import React, { useEffect } from "react";
import { useAuthStore } from "../../store/authStore";
import { useResumeStore } from "../../store/useResumeDetailStore";

const GaneshTemplate = () => {
  const { user, fetchCurrentUser } = useAuthStore();
  const { resume, fetchResume } = useResumeStore();

  useEffect(() => {
    if (!user) fetchCurrentUser();
  }, []);

  useEffect(() => {
    if (user?.resumeId && !resume) fetchResume(user.resumeId);
  }, [user]);

  if (!resume || !user) {
    return <p className="text-center mt-20 text-gray-500">Loading resume...</p>;
  }

  const {
    Title,
    Email,
    Headline,
    Phone,
    Location,
    Website,
    ProfilePic,
    Profiles = [],
    Experience = [],
    Education = [],
    Skills = [],
    Languages = [],
    Certifications = [],
    Awards = [],
    Projects = [],
    Publications = [],
    Volunteering = [],
    References = [],
    Interests = [],
    summery = "",
  } = resume;

  const Section = ({ title, children }) => (
    <div className="text-center border-t border-gray-300 py-4">
      <h2 className="text-lg font-bold text-blue-800 mb-2">{title}</h2>
      <div className="text-sm text-gray-700 max-w-2xl mx-auto leading-relaxed">
        {children}
      </div>
    </div>
  );

  return (
    <div className="w-[794px] mx-auto bg-white text-black font-sans p-6 border shadow-md rounded-md">
      {/* Header */}
      <div className="text-center border-b border-gray-400 pb-4 mb-4">
        <h1 className="text-3xl font-extrabold text-gray-900">{Title}</h1>
        {Headline && (
          <p className="text-base italic text-gray-600">{Headline}</p>
        )}
        <div className="text-sm text-gray-700 mt-2">
          {Email && <p>Email: {Email}</p>}
          {Phone && <p>Phone: {Phone}</p>}
          {Location && <p>Location: {Location}</p>}
          {Website && <p>Website: {Website}</p>}
        </div>
      </div>

      {summery && (
        <Section title="Summary">
          <p>{summery}</p>
        </Section>
      )}

      {Skills.length > 0 && (
        <Section title="Skills">
          <ul className="list-disc list-inside">
            {Skills.map((skill) => (
              <li key={skill._id}>
                {skill.Skill} - {skill.Proficiency}
              </li>
            ))}
          </ul>
        </Section>
      )}

      {Experience.length > 0 && (
        <Section title="Experience">
          {Experience.map((exp) => (
            <div key={exp._id} className="mb-2">
              <p className="font-medium">
                {exp.Position} at {exp.Company}
              </p>
              <p>{exp.Location}</p>
              <p>
                {new Date(exp.StartDate).toLocaleDateString()} -{" "}
                {new Date(exp.EndDate).toLocaleDateString()}
              </p>
              {exp.Description && <p>{exp.Description}</p>}
            </div>
          ))}
        </Section>
      )}

      {Education.length > 0 && (
        <Section title="Education">
          {Education.map((edu) => (
            <div key={edu._id} className="mb-2">
              <p className="font-medium">
                {edu.Degree} - {edu.Institution}
              </p>
              <p>{edu.Location}</p>
              <p>
                {new Date(edu.StartDate).toLocaleDateString()} -{" "}
                {new Date(edu.EndDate).toLocaleDateString()}
              </p>
            </div>
          ))}
        </Section>
      )}

      {Projects.length > 0 && (
        <Section title="Projects">
          {Projects.map((proj) => (
            <div key={proj._id} className="mb-2">
              <p className="font-medium">{proj.Title}</p>
              <p>{proj.Description}</p>
              {proj.Technologies?.length > 0 && (
                <p>Tech: {proj.Technologies.join(", ")}</p>
              )}
              {proj.Link && <p>Link: {proj.Link}</p>}
            </div>
          ))}
        </Section>
      )}

      {Languages.length > 0 && (
        <Section title="Languages">
          <ul className="list-disc list-inside">
            {Languages.map((lang) => (
              <li key={lang._id}>
                {lang.Name} - {lang.Proficiency}
              </li>
            ))}
          </ul>
        </Section>
      )}

      {Profiles.length > 0 && (
        <Section title="Social Profiles">
          <ul className="list-disc list-inside">
            {Profiles.map((p) => (
              <li key={p._id}>
                {p.Network}: {p.Username}
              </li>
            ))}
          </ul>
        </Section>
      )}
    </div>
  );
};

export default GaneshTemplate;
