import React, { useEffect } from "react";
import { useUserInfoStore } from "../../../store/userInfoStore";

const ProfilePicture = () => {
  const { userInfo, fetchUserInfo } = useUserInfoStore();

  useEffect(() => {
    fetchUserInfo();
  }, [fetchUserInfo]);

  if (!userInfo?.ProfilePic) return null;

  return (
    <div className="flex justify-center mb-4">
      <img
        src={userInfo.ProfilePic}
        alt="Profile"
        className="w-24 h-24 rounded-full border-2 border-gray-300 shadow-md object-cover"
      />
    </div>
  );
};

export default ProfilePicture;
