import React, { useState, useEffect } from "react";
import {
  FaTwi<PERSON>,
  FaFacebookF,
  FaInstagram,
  FaLinkedinIn,
  FaYoutube,
  FaFeather,
  FaRegHeart,
  FaGlobe,
} from "react-icons/fa";
import { motion } from "framer-motion";

const socialLinks = [
  { href: "https://x.com/", icon: FaTwitter, label: "X" },
  { href: "https://facebook.com/", icon: FaFacebookF, label: "Facebook" },
  { href: "https://instagram.com/", icon: FaInstagram, label: "Instagram" },
  { href: "https://linkedin.com/", icon: FaLinkedinIn, label: "LinkedIn" },
  { href: "https://youtube.com/", icon: FaYoutube, label: "YouTube" },
  { href: "#", icon: FaFeather, label: "Butterfly" },
];

const Footer = ({ theme = "dark" }) => {
  const [showTop, setShowTop] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setShowTop(window.scrollY > 300);
    };
    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const isDark = theme === "dark";
  const iconSize = {
    mobile: 22,
    desktop: 28,
  };
  const textColor = isDark ? "text-white" : "text-[#29354d]";
  const bgColor = isDark ? "bg-[#0a1736]" : "bg-[#f5f7fb]";
  const borderColor = isDark ? "border-[#223056]" : "border-[#ccc]";

  return (
    <footer className={`${bgColor} w-full pt-12 pb-6 border-t ${borderColor}`}>
      <div className="max-w-7xl mx-auto px-4 flex flex-col md:flex-row flex-wrap items-center justify-between gap-6">

        <div className="flex items-center gap-2">
          <span className={`text-base font-medium ${textColor}`}>
            Made with{" "}
            <span className="text-[#fcc250]">
              <FaRegHeart />
            </span>{" "}
            by Medin Technology©
          </span>
        </div>

        <div>
          <motion.button
            whileHover={{ scale: 1.07 }}
            whileTap={{ scale: 0.95 }}
            className={`flex items-center gap-2 px-4 py-2 rounded-md ${
              isDark ? "bg-[#1b294b] text-white" : "bg-[#29354d] text-white"
            } font-medium hover:bg-[#233366] transition`}
            aria-label="Change Language"
          >
            <FaGlobe />
            English
          </motion.button>
        </div>

        <div
          className={`flex items-center justify-center gap-5 ${textColor} text-2xl`}
        >
          {socialLinks.map((link) => (
            <motion.a
              key={link.label}
              href={link.href}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={link.label}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              className={`hover:text-[#fcc250] transition`}
            >
              <link.icon
                size={
                  window.innerWidth > 768 ? iconSize.desktop : iconSize.mobile
                }
              />
            </motion.a>
          ))}
        </div>
      </div>

      <div className={`border-t ${borderColor} mt-12 pt-8`}>
        <div className="max-w-7xl mx-auto px-4 flex justify-center flex-wrap items-end gap-6">
          {["footer1.png", "footer2.png", "footer3.png"].map((img, idx) => (
            <motion.img
              key={idx}
              src={`public/footer/${img}`}
              alt={`Footer Resume ${idx + 1}`}
              className="h-28 md:h-36 w-auto object-contain rounded-lg shadow-lg hover:scale-105 transition"
              whileHover={{ scale: 1.07 }}
              whileTap={{ scale: 0.95 }}
            />
          ))}
        </div>
      </div>

      {showTop && (
        <motion.button
          onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
          className={`fixed bottom-6 right-6 p-3 rounded-full ${
            isDark ? "bg-[#fcc250] text-[#0a1736]" : "bg-[#29354d] text-white"
          } shadow-lg hover:scale-105 transition`}
          aria-label="Back to top"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          ↑
        </motion.button>
      )}
    </footer>
  );
};

export default Footer;
