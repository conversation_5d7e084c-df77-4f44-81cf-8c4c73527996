import React from "react";
import styled, { keyframes } from "styled-components";

// Keyframes for infinite looping resume animation
const slide = keyframes`
  0% { transform: translateX(0); }
  100% { transform: translateX(-33.333%); }
`;

const BannerWrapper = styled.section`
  display: flex;
  align-items: center;
  min-height: 520px;
  background: linear-gradient(90deg, #f7fafd 40%, #fff 100%);
  overflow: hidden;
  position: relative;
  @media (max-width: 900px) {
    flex-direction: column;
    min-height: 700px;
    padding-top: 2rem;
  }
`;

const BannerContent = styled.div`
  flex: 1;
  padding-left: 4vw;
  z-index: 2;
  @media (max-width: 900px) {
    padding-left: 0;
    text-align: center;
    padding-bottom: 2rem;
  }
`;

const BannerHeadline = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1rem;
  color: #191c21;
  @media (max-width: 600px) {
    font-size: 2.2rem;
  }
`;

const BannerSub = styled.p`
  font-size: 1.3rem;
  color: #6b7280;
  margin-bottom: 2.5rem;
`;

const ButtonsRow = styled.div`
  display: flex;
  gap: 1.2rem;
  align-items: center;
  margin-bottom: 1.5rem;
  @media (max-width: 600px) {
    flex-direction: column;
    gap: 0.75rem;
  }
`;

const MainBtn = styled.a`
  background: #00ffb6;
  color: #191c21;
  font-weight: 700;
  font-size: 1.15rem;
  border-radius: 999px;
  padding: 0.9rem 2.5rem;
  text-decoration: none;
  transition: background 0.2s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.09);
  &:hover {
    background: #11e6a4;
  }
`;

const OutlineBtn = styled.a`
  background: #fff;
  border: 2px solid #191c21;
  color: #191c21;
  font-weight: 700;
  font-size: 1.15rem;
  border-radius: 999px;
  padding: 0.9rem 2.5rem;
  text-decoration: none;
  transition: background 0.15s, color 0.15s;
  &:hover {
    background: #f7f7f8;
  }
`;

const AIPowered = styled.span`
  background: #ffe9c2;
  color: #191c21;
  font-size: 1rem;
  border-radius: 10px;
  padding: 0.35rem 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  margin-left: 1rem;
  svg {
    margin-right: 0.45rem;
  }
  @media (max-width: 600px) {
    margin-left: 0;
    justify-content: center;
  }
`;

// Resume Animation Styles
const BannerVisual = styled.div`
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  height: 100%;
  min-width: 0;
  position: relative;
  overflow: visible;
  @media (max-width: 900px) {
    width: 100%;
    justify-content: center;
  }
`;

const ResumeSliderOuter = styled.div`
  width: 470px;
  height: 470px;
  overflow: hidden;
  background: transparent;
  position: relative;
  display: flex;
  align-items: center;
  @media (max-width: 600px) {
    width: 90vw;
    height: 340px;
  }
`;

const ResumeSliderInner = styled.div`
  display: flex;
  width: calc(470px * 6); /* 3 images, repeated for infinite animation */
  animation: ${slide} 14s linear infinite;
  @media (max-width: 600px) {
    width: 270vw;
  }
`;

const ResumeImageWrap = styled.div`
  width: 470px;
  flex: 0 0 470px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding: 0 0.7rem;
  filter: drop-shadow(0 12px 32px rgba(60, 70, 100, 0.1));
  @media (max-width: 600px) {
    width: 90vw;
    flex: 0 0 90vw;
    padding: 0 0.1rem;
  }
`;

const ResumeImg = styled.img`
  width: 340px;
  height: 440px;
  border-radius: 18px;
  background: #fff;
  object-fit: cover;
  border: 2px solid #e5e7eb;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.13);
  @media (max-width: 900px) {
    width: 220px;
    height: 300px;
  }
`;

// Decorative dots
const Dots = styled.div`
  position: absolute;
  left: 0;
  bottom: 40px;
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 12px;
  z-index: 1;
  @media (max-width: 600px) {
    display: none;
  }
  div {
    width: 14px;
    height: 14px;
    background: #7a5cff;
    opacity: 0.18;
    border-radius: 50%;
    animation: ${fadeDot} 1.5s linear infinite alternate;
    &:nth-child(2n) {
      animation-delay: 0.6s;
    }
    &:nth-child(3n) {
      animation-delay: 1s;
    }
  }
`;

const fadeDot = keyframes`
  0% { opacity: 0.18; }
  100% { opacity: 0.35; }
`;

// Main Banner Component
const AnimatedResumeBanner = () => {
  // Infinite slider: repeat the images at least twice for seamless loop
  const resumeImgs = [
    "/public/templet1.png",
    "/public/templet2.png",
    "/public/templet3.png",
  ];
  const sliderImgs = [...resumeImgs, ...resumeImgs];

  return (
    <BannerWrapper>
      <BannerContent>
        <BannerHeadline>
          The Resume that gets
          <br /> the job... <span style={{ color: "#00ffb6" }}>done</span>
        </BannerHeadline>
        <BannerSub>
          Build a new Resume or improve your existing one
          <br />
          with step-by-step expert guidance.
        </BannerSub>
        <ButtonsRow>
          <MainBtn href="/register">Create your Resume</MainBtn>
          <OutlineBtn href="/upgrade">Upgrade a Resume</OutlineBtn>
          <AIPowered>
            <svg width="18" height="18" viewBox="0 0 24 24" fill="#f59e42">
              <path d="M19.07,4.93a10,10,0,1,0,0,14.14A10,10,0,0,0,19.07,4.93ZM12,20a8,8,0,1,1,5.66-13.66A8,8,0,0,1,12,20Z" />
              <circle cx="12" cy="12" r="3.5" fill="#f59e42" />
            </svg>
            AI-powered
          </AIPowered>
        </ButtonsRow>
      </BannerContent>
      <BannerVisual>
        <ResumeSliderOuter>
          <ResumeSliderInner>
            {sliderImgs.map((src, idx) => (
              <ResumeImageWrap key={idx}>
                <ResumeImg src={src} alt={`Resume Template ${(idx % 3) + 1}`} />
              </ResumeImageWrap>
            ))}
          </ResumeSliderInner>
          <Dots>
            {[...Array(18)].map((_, i) => (
              <div key={i} />
            ))}
          </Dots>
        </ResumeSliderOuter>
      </BannerVisual>
    </BannerWrapper>
  );
};

export default AnimatedResumeBanner;
