import React, { useEffect, useState } from "react";
import Banner from "../components/Homepage/Banner";
import Templatesection from "../components/Homepage/Templatesection";
import Process from "../components/Homepage/Process";
import HomepageLayout from "../components/Layout/HomepageLayout";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../store/authStore";

const Home = () => {
  const navigate = useNavigate();
  const { user, isLoading, fetchCurrentUser } = useAuthStore();
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false);

  useEffect(() => {
    const loadUser = async () => {
      try {
        await fetchCurrentUser();
      } finally {
        setHasCheckedAuth(true);
      }
    };
    loadUser();
  }, []);

  useEffect(() => {
    if (hasCheckedAuth && !isLoading && user) {
      navigate("/dashboard", { replace: true });
    }
  }, [user, isLoading, hasCheckedAuth, navigate]);

  if (!hasCheckedAuth || isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p className="text-lg font-semibold">Loading...</p>
      </div>
    );
  }

  return (
    <HomepageLayout>
      <main className="flex-1">
        <Banner />
        <Templatesection />
        <Process iconSize="w-12 h-12 md:w-20 md:h-20" />
      </main>
    </HomepageLayout>
  );
};

export default Home;
