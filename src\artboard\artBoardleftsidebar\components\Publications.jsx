import React, { useEffect, useState } from "react";
import { FaBook<PERSON>pen, FaPlus, FaBars, FaTimes, FaEdit } from "react-icons/fa";
import { useResumeStore } from "../../../store/useResumeDetailStore";
import { useAddInfoStore } from "../../../store/addInfoStore";
import { useDeleteInfoStore } from "../../../store/deleteInfoStore";
import { useUpdateInfoStore } from "../../../store/updateInfoStore";
import DataCard from "../../components/DataCard";
import toast from "react-hot-toast";

const Publications = () => {
  const { resume, fetchResume, isLoading } = useResumeStore();
  const addInfo = useAddInfoStore((state) => state.addInfo);
  const deleteInfo = useDeleteInfoStore((state) => state.deleteInfo);
  const updateInfo = useUpdateInfoStore((state) => state.updateInfo);
  const resumeId = resume?._id;
  const publications = resume?.Publications || [];

  const [showModal, setShowModal] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(null);
  const [formData, setFormData] = useState({
    title: "",
    publisher: "",
    date: "",
    website: "",
    summary: "",
  });

  useEffect(() => {
    if (resumeId) fetchResume(resumeId);
  }, [resumeId]);

  const handleChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const resetForm = () => {
    setFormData({
      title: "",
      publisher: "",
      date: "",
      website: "",
      summary: "",
    });
    setEditingId(null);
  };

  const handleDeleteConfirmed = async () => {
    if (!resumeId || !confirmDelete) return;
    try {
      await deleteInfo({
        resumeId,
        section: "publications",
        entryId: confirmDelete._id,
      });
      toast.success("Publication deleted.");
      setConfirmDelete(null);
      await fetchResume(resumeId);
    } catch (err) {
      toast.error("Failed to delete publication.");
    }
  };

  const handleEdit = (pub) => {
    setFormData({
      title: pub.Title || "",
      publisher: pub.Publisher || "",
      date: pub.Date?.split("T")[0] || "",
      website: pub.Website || "",
      summary: pub.Description || "",
    });
    setEditingId(pub._id);
    setShowModal(true);
  };

  const handleCreateOrUpdate = async () => {
    const { title, publisher, date } = formData;
    if (!title || !publisher || !date) {
      toast.error("Title, Publisher, and Date are required.");
      return;
    }

    const payload = {
      Title: title,
      Publisher: publisher,
      Date: date,
      Website: formData.website,
      Description: formData.summary,
    };

    try {
      if (editingId) {
        await updateInfo({
          resumeId,
          section: "publications",
          entryId: editingId,
          updatedData: payload,
        });
        toast.success("Publication updated!");
      } else {
        await addInfo({
          resumeId,
          section: "publications",
          newData: payload,
        });
        toast.success("Publication added!");
      }

      resetForm();
      setShowModal(false);
      await fetchResume(resumeId);
    } catch (err) {
      toast.error("Error saving publication.");
    }
  };

  return (
    <div className="bg-white min-h-screen text-[#29354d] p-6 font-sans">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="bg-gray-100 p-2 rounded-full">
            <FaBookOpen size={18} />
          </div>
          <h2 className="text-2xl font-bold">Publications</h2>
        </div>
        <FaBars size={20} className="text-gray-400" />
      </div>

      {/* Add Button */}
      <div
        onClick={() => {
          resetForm();
          setShowModal(true);
        }}
        className="cursor-pointer border-2 border-dashed border-gray-400 rounded-lg py-5 px-8 flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-[#29354d] text-lg font-medium mb-8 transition"
      >
        <FaPlus className="mr-2" /> Add a new publication
      </div>

      {/* Publications List */}
      <div className="flex flex-wrap gap-4">
        {isLoading ? (
          <p className="text-gray-500">Loading publications...</p>
        ) : publications.length === 0 ? (
          <p className="text-gray-500">No publications added yet.</p>
        ) : (
          publications.map((pub) => (
            <div key={pub._id} className="w-fit min-w-[250px]">
              <DataCard
                title={pub.Title}
                subtitle={
                  pub.Date
                    ? `${pub.Publisher || "Unknown"} • ${pub.Date}`
                    : pub.Publisher
                }
                description={
                  <>
                    {pub.Description && (
                      <p className="mb-1">{pub.Description}</p>
                    )}
                    {pub.Website && (
                      <p className="text-sm text-blue-600 break-words">
                        <a
                          href={pub.Website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="underline"
                        >
                          {pub.Website}
                        </a>
                      </p>
                    )}
                  </>
                }
                onDelete={() => setConfirmDelete(pub)}
                onEdit={() => handleEdit(pub)}
              />
            </div>
          ))
        )}
      </div>

      {/* Create/Update Modal */}
      {showModal && (
        <div className="fixed inset-0 z-50 bg-black/70 flex items-center justify-center px-4">
          <div className="bg-white p-8 rounded-xl w-full max-w-lg shadow-2xl relative text-[#29354d]">
            <button
              onClick={() => {
                setShowModal(false);
                resetForm();
              }}
              className="absolute top-4 right-4 text-2xl text-gray-400 hover:text-black"
            >
              <FaTimes />
            </button>

            <h3 className="text-xl font-bold mb-6 flex items-center gap-2">
              {editingId ? (
                <>
                  <FaEdit /> Edit Publication
                </>
              ) : (
                <>
                  <FaPlus /> Add Publication
                </>
              )}
            </h3>

            <form onSubmit={(e) => e.preventDefault()} className="space-y-4">
              <InputField
                label="Title"
                value={formData.title}
                onChange={(e) => handleChange("title", e.target.value)}
                required
                placeholder="e.g. Deep Learning for NLP"
              />
              <InputField
                label="Publisher"
                value={formData.publisher}
                onChange={(e) => handleChange("publisher", e.target.value)}
                required
                placeholder="e.g. Springer"
              />
              <InputField
                label="Date"
                value={formData.date}
                onChange={(e) => handleChange("date", e.target.value)}
                required
                type="date"
              />
              <InputField
                label="Website"
                value={formData.website}
                onChange={(e) => handleChange("website", e.target.value)}
                placeholder="https://example.com"
              />

              <div>
                <label className="block mb-1 text-sm font-medium">
                  Summary
                </label>
                <textarea
                  rows={4}
                  value={formData.summary}
                  onChange={(e) => handleChange("summary", e.target.value)}
                  placeholder="Brief description of the publication..."
                  className="w-full bg-gray-50 border border-gray-400 rounded-lg px-4 py-2 text-[#29354d]"
                />
              </div>

              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={handleCreateOrUpdate}
                  className="bg-[#a5a29c] hover:bg-[#000000] text-[#000000] hover:text-white px-6 py-2 rounded-lg transition font-semibold"
                >
                  {editingId ? "Update" : "Create"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {confirmDelete && (
        <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 px-4">
          <div className="bg-white max-w-md w-full p-6 rounded-lg shadow-xl relative">
            <button
              onClick={() => setConfirmDelete(null)}
              className="absolute top-4 right-4 text-gray-500 hover:text-black"
            >
              <FaTimes size={20} />
            </button>
            <h3 className="text-lg font-bold mb-2 text-black">
              Delete Publication
            </h3>
            <p className="text-sm mb-4 text-gray-700">
              Are you sure you want to delete this publication?
            </p>

            <div className="border p-3 rounded bg-gray-50 text-sm mb-4 text-[#29354d]">
              <p>
                <strong>Title:</strong> {confirmDelete.Title}
              </p>
              <p>
                <strong>Publisher:</strong> {confirmDelete.Publisher}
              </p>
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setConfirmDelete(null)}
                className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-sm"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirmed}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 text-sm"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Reusable InputField component
const InputField = ({ label, required, ...props }) => (
  <div>
    <label className="block mb-1 text-sm font-medium">
      {label} {required && <span className="text-red-500">*</span>}
    </label>
    <input
      {...props}
      className="w-full bg-gray-50 border border-gray-400 rounded-lg px-4 py-2 text-[#29354d]"
    />
  </div>
);

export default Publications;
