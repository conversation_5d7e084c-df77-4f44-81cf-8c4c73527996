import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { FaFolderO<PERSON>, FaBars, FaPlus, FaTimes } from "react-icons/fa";

/* ---------- global stores ---------- */
import { useResumeStore } from "../../../store/useResumeDetailStore";
import { useAddInfoStore } from "../../../store/addInfoStore";
import { useUpdateInfoStore } from "../../../store/updateInfoStore";
import { useDeleteInfoStore } from "../../../store/deleteInfoStore";

/* ---------- UI ---------- */
import DataCard from "../../Components/DataCard";
import ProjectModal from "../../Components/ProjectModal";
import toast from "react-hot-toast";

const Projects = () => {
  const { resumeId } = useParams();
  const { resume, fetchResume, loading } = useResumeStore();
  const { addInfo } = useAddInfoStore();
  const { updateInfo } = useUpdateInfoStore();
  const { deleteInfo } = useDeleteInfoStore();

  const projects = resume?.Projects ?? [];

  const [modalOpen, setModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(null);

  useEffect(() => {
    if (resumeId) fetchResume(resumeId);
  }, [resumeId]);

  const handleDeleteConfirmed = async () => {
    if (!resumeId || !confirmDelete) return;
    try {
      await deleteInfo({
        resumeId,
        section: "projects",
        entryId: confirmDelete._id,
      });
      toast.success("Project deleted.");
      await fetchResume(resumeId);
    } catch (err) {
      toast.error("Failed to delete project.");
    } finally {
      setConfirmDelete(null);
    }
  };

  const handleAddOrUpdate = async (payload, entryId = null) => {
    if (!resumeId) return;

    try {
      if (entryId) {
        await updateInfo({
          resumeId,
          section: "projects",
          entryId,
          updatedData: payload,
        });
        toast.success("Project updated!");
      } else {
        await addInfo({
          resumeId,
          section: "projects",
          newData: payload,
        });
        toast.success("Project added!");
      }

      await fetchResume(resumeId);
      setModalOpen(false);
      setEditData(null);
    } catch (err) {
      toast.error("Failed to save project.");
    }
  };

  return (
    <div className="bg-white min-h-screen py-10 px-4 md:px-8 text-[#29354d] font-sans">
      {/* Header */}
      <header className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <FaFolderOpen className="text-[#29354d]" />
          <h2 className="text-2xl font-extrabold">Projects</h2>
        </div>
        <FaBars className="text-[#29354d]" />
      </header>

      {/* Add Button */}
      <button
        type="button"
        onClick={() => {
          setEditData(null);
          setModalOpen(true);
        }}
        className="cursor-pointer border-2 border-dashed border-gray-400 rounded-lg py-5 px-8 flex items-center justify-center bg-gray-200 hover:bg-gray-300 text-lg font-medium mb-8 w-full md:w-auto"
      >
        <FaPlus className="mr-2" /> Add a new project
      </button>

      {/* Projects List */}
      {loading ? (
        <p className="text-gray-500">Loading…</p>
      ) : projects.length === 0 ? (
        <p className="text-gray-500">No projects added yet.</p>
      ) : (
        <div className="flex flex-wrap gap-4">
          {projects.map((proj) => (
            <div key={proj._id} className="w-fit min-w-[250px]">
              <DataCard
                title={proj.Title}
                subtitle={
                  proj.Link ? (
                    <a
                      href={proj.Link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 underline"
                    >
                      {proj.Link}
                    </a>
                  ) : (
                    "No link provided"
                  )
                }
                description={
                  proj.Description
                    ? proj.Description
                    : proj.Technologies?.length
                    ? `Tech Stack: ${proj.Technologies.join(", ")}`
                    : "No description"
                }
                onDelete={() => setConfirmDelete(proj)}
                onEdit={() => {
                  setEditData(proj);
                  setModalOpen(true);
                }}
              />
            </div>
          ))}
        </div>
      )}

      {/* Project Modal */}
      {modalOpen && (
        <ProjectModal
          initial={editData}
          onClose={() => {
            setModalOpen(false);
            setEditData(null);
          }}
          onSave={handleAddOrUpdate}
        />
      )}

      {/* Delete Confirmation Modal */}
      {confirmDelete && (
        <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 px-4">
          <div className="bg-white max-w-md w-full p-6 rounded-lg shadow-xl relative">
            <button
              onClick={() => setConfirmDelete(null)}
              className="absolute top-4 right-4 text-gray-500 hover:text-black"
            >
              <FaTimes size={20} />
            </button>
            <h3 className="text-lg font-bold mb-2 text-[#29354d]">
              Delete Project
            </h3>
            <p className="text-sm mb-4 text-gray-700">
              Are you sure you want to delete this project?
            </p>

            <div className="border p-3 rounded bg-gray-50 text-sm mb-4 text-[#29354d]">
              <p>
                <strong>Title:</strong> {confirmDelete.Title}
              </p>
              {confirmDelete.Link && (
                <p>
                  <strong>Link:</strong>{" "}
                  <a
                    href={confirmDelete.Link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 underline"
                  >
                    {confirmDelete.Link}
                  </a>
                </p>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setConfirmDelete(null)}
                className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-sm"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirmed}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 text-sm"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Projects;
