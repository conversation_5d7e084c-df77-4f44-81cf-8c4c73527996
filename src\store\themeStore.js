import { create } from "zustand";
import { TEMPLATES } from "../artboard/Template";

export const useThemeStore = create((set) => ({
    primaryColor: "#fcc250",
    backgroundColor: "#ffffff",
    textColor: "#29354d",

    setPrimaryColor: (color) => set({ primaryColor: color }),
    setBackgroundColor: (color) => set({ backgroundColor: color }),
    setTextColor: (color) => set({ textColor: color }),

    resetTheme: (templateId) => {
        const key = templateId.toLowerCase();
        const template = TEMPLATES[key];
        if (template?.defaultTheme) {
            const { primaryColor, backgroundColor, textColor } = template.defaultTheme;
            set({ primaryColor, backgroundColor, textColor });
        } else {
            set({
                primaryColor: "#fcc250",
                backgroundColor: "#ffffff",
                textColor: "#29354d",
            });
        }
    },
}));