import { create } from "zustand";
import axios from "axios";
import { ADDINFO_ENDPOINTS, DELETE_INFO, UPDATEINFO_ENDPOINTS } from "../lib/constants";
import { useUserInfoStore } from "./userInfoStore";

export const useExperienceStore = create((set) => ({
    experiences: [],
    isLoading: false,
    error: null,

    fetchExperiences: async () => {
        set({ isLoading: true, error: null });
        try {
            const { userInfo, fetchUserInfo } = useUserInfoStore.getState();
            let user = userInfo;

            console.log("Calling for the Experience:");

            if (!user) {
                await fetchUserInfo();
                user = useUserInfoStore.getState().userInfo;
            }

            set({
                experiences: user?.Experience || [],
                isLoading: false,
            });
        } catch (err) {
            console.error("Error fetching experiences:", err);
            set({
                error: "Failed to load experiences",
                isLoading: false,
            });
        }
    },

    addExperience: async (data) => {
        try {
            const res = await axios.post(ADDINFO_ENDPOINTS.EXPERIENCE, data, {
                withCredentials: true,
            });
            set((state) => ({
                experiences: [...state.experiences, res.data],
            }));
        } catch (err) {
            console.error("Error adding experience:", err.response?.data || err.message);
        }
    },

    deleteExperience: async (id) => {
        try {
            await axios.delete(DELETE_INFO.EXPERIENCE(id), {
                withCredentials: true,
            });
            set((state) => ({
                experiences: state.experiences.filter((exp) => exp._id !== id),
            }));
        } catch (err) {
            console.error("Error deleting experience:", err.response?.data || err.message);
        }
    },


    updateExperience: async (id, data) => {
        try {
            const res = await axios.put(`${UPDATEINFO_ENDPOINTS.EXPERIENCE}/${id}`, data, {
                withCredentials: true,
            });
            set((state) => ({
                experiences: state.experiences.map((exp) =>
                    exp._id === id ? res.data : exp
                ),
            }));
        } catch (err) {
            console.error("Error updating experience:", err.response?.data || err.message);
        }
    },
}));
