import React from "react";

const Publications = ({
  title = "Publications",
  publications = [],
  sectionStyle = "",
  titleStyle = "",
  itemStyle = "",
  nameStyle = "",
  publisherStyle = "",
  dateStyle = "",
  summaryStyle = "",
  linkStyle = "",
}) => {
  if (!publications || publications.length === 0) return null;

  return (
    <section className={sectionStyle}>
      {title && (
        <h3 className={`text-lg font-bold mb-2 ${titleStyle}`}>{title}</h3>
      )}
      <ul className="space-y-4">
        {publications.map((pub, idx) => (
          <li key={idx} className={itemStyle}>
            <div className="flex justify-between items-center">
              <h4 className={`text-base font-semibold ${nameStyle}`}>
                {pub.name}
              </h4>
              {pub.date && (
                <span className={`text-sm text-gray-500 italic ${dateStyle}`}>
                  {pub.date}
                </span>
              )}
            </div>
            {pub.publisher && (
              <div className={`text-sm text-gray-700 ${publisherStyle}`}>
                {pub.publisher}
              </div>
            )}
            {pub.summary && (
              <p className={`text-sm text-gray-600 mt-1 ${summaryStyle}`}>
                {pub.summary}
              </p>
            )}
            {pub.link && (
              <a
                href={pub.link}
                target="_blank"
                rel="noopener noreferrer"
                className={`text-sm text-blue-600 hover:underline mt-1 inline-block ${linkStyle}`}
              >
                {pub.linkLabel || "View Publication"}
              </a>
            )}
          </li>
        ))}
      </ul>
    </section>
  );
};

export default Publications;
