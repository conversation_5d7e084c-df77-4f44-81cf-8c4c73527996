import React, { useEffect, useRef, useState } from "react";
import { FaUser } from "react-icons/fa";
import { useAuthStore } from "../../../store/authStore";
import { useImageStore } from "../../../store/imageStore";
import { useResumeStore } from "../../../store/useResumeDetailStore";
import { debounce } from "../../../utils/debounce";

const Basics = () => {
  const user = useAuthStore((state) => state.user);
  const resume = useResumeStore((state) => state.resume);
  const updateBasicsField = useResumeStore((state) => state.updateBasicsField);

  const { uploadImage, deleteImage, uploading } = useImageStore();

  const [data, setData] = useState({
    profilePic: "",
    name: "",
    headline: "",
    email: "",
    website: "",
    phone: "",
    location: "",
  });

  const prevSnapshot = useRef(null);

  const isEqual = (a, b) => JSON.stringify(a) === JSON.stringify(b);

  useEffect(() => {
    if (!resume) return;

    const snapshot = {
      profilePic: resume?.ProfilePic || "",
      name: user?.name || user?.Name || "",
      headline: resume?.Headline || "",
      email: user?.email || "",
      website: resume?.Website || "",
      phone: resume?.Phone || "",
      location: resume?.Location || "",
    };

    if (!isEqual(prevSnapshot.current, snapshot)) {
      prevSnapshot.current = snapshot;
      setData(snapshot);
    }
  }, [
    resume,
    resume?.ProfilePic,
    resume?.Headline,
    resume?.Website,
    resume?.Phone,
    resume?.Location,
    user?.name,
    user?.Name,
    user?.email,
  ]);

  const autoSave = useRef(
    debounce((key, value) => {
      updateBasicsField(key, value);
    }, 600)
  ).current;

  const handleChange = (key, value) => {
    setData((prev) => ({ ...prev, [key]: value }));

    const mappedKeys = {
      headline: "Headline",
      website: "Website",
      phone: "Phone",
      location: "Location",
    };

    if (mappedKeys[key]) {
      autoSave(mappedKeys[key], value);
    }
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      if (data.profilePic) await deleteImage();
      const newUrl = await uploadImage(file);
      if (newUrl) {
        setData((prev) => ({ ...prev, profilePic: newUrl }));
        updateBasicsField("ProfilePic", newUrl);
      }
    } catch (err) {
      console.error("Image upload failed", err);
    }
  };

  const handleImageDelete = async () => {
    if (window.confirm("Delete your profile picture?")) {
      try {
        await deleteImage();
        setData((prev) => ({ ...prev, profilePic: "" }));
        updateBasicsField("ProfilePic", "");
      } catch (err) {
        console.error("Image deletion failed", err);
      }
    }
  };

  if (!resume) return <p className="p-4">Loading resume...</p>;

  return (
    <div className="bg-white min-h-screen py-10 px-4 md:px-8 text-[#29354d] font-sans">
      {/* Header */}
      <div className="flex items-center gap-3 mb-8">
        <FaUser size={24} className="text-[#010101]" />
        <div>
          <h2 className="text-2xl font-bold">Basic Info</h2>
          {data.name && (
            <p className="text-sm text-gray-600">
              Welcome, <span className="font-semibold">{data.name}</span>
            </p>
          )}
        </div>
      </div>

      {/* Profile Picture */}
      <div className="mb-6">
        <label className="block text-sm font-medium mb-1">
          Profile Picture
        </label>
        {data.profilePic ? (
          <div className="flex items-center gap-4">
            <img
              src={data.profilePic}
              alt="Profile"
              className="h-16 w-16 rounded-full object-cover border border-gray-300"
            />
            <div className="flex flex-col gap-1">
              <button
                onClick={handleImageDelete}
                className="text-red-600 text-sm hover:underline"
              >
                Delete
              </button>
              <label className="text-[#fcc250] text-sm hover:underline cursor-pointer">
                Change
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  disabled={uploading}
                />
              </label>
            </div>
          </div>
        ) : (
          <div>
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              disabled={uploading}
              className="text-sm"
            />
            <p className="text-xs text-gray-500 mt-1">No image uploaded</p>
          </div>
        )}
        {uploading && (
          <p className="text-xs text-blue-600 mt-1">Uploading...</p>
        )}
      </div>

      {/* Input Fields */}
      <div className="space-y-4">
        {/* Name */}
        <div>
          <label className="block text-sm font-medium mb-1">Full Name</label>
          <input
            type="text"
            value={data.name}
            className="w-full border border-gray-300 rounded px-3 py-2 bg-gray-100"
            readOnly
            disabled
          />
        </div>

        {/* Headline */}
        <div>
          <label className="block text-sm font-medium mb-1">Headline</label>
          <input
            type="text"
            value={data.headline}
            onChange={(e) => handleChange("headline", e.target.value)}
            className="w-full border border-gray-300 rounded px-3 py-2"
            placeholder="e.g. Full Stack Developer"
          />
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium mb-1">Email</label>
          <input
            type="email"
            value={data.email}
            className="w-full border border-gray-300 rounded px-3 py-2 bg-gray-100"
            readOnly
            disabled
          />
        </div>

        {/* Website */}
        <div>
          <label className="block text-sm font-medium mb-1">Website</label>
          <input
            type="url"
            value={data.website}
            onChange={(e) => handleChange("website", e.target.value)}
            className="w-full border border-gray-300 rounded px-3 py-2"
            placeholder="https://your-portfolio.com"
          />
        </div>

        {/* Phone */}
        <div>
          <label className="block text-sm font-medium mb-1">Phone</label>
          <input
            type="text"
            value={data.phone}
            onChange={(e) => handleChange("phone", e.target.value)}
            className="w-full border border-gray-300 rounded px-3 py-2"
            placeholder="+91 9876543210"
          />
        </div>

        {/* Location */}
        <div>
          <label className="block text-sm font-medium mb-1">Location</label>
          <input
            type="text"
            value={data.location}
            onChange={(e) => handleChange("location", e.target.value)}
            className="w-full border border-gray-300 rounded px-3 py-2"
            placeholder="City, Country"
          />
        </div>
      </div>
    </div>
  );
};

export default Basics;
