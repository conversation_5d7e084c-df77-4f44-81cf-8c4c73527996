import React from "react";
import { useThemeStore } from "../../../store/themeStore";
import { useTemplateStore } from "../../../store/templateStore";

const presetColors = [
  "#29354d",
  "#fcc250",
  "#f3f4f6",
  "#111827",
  "#f87171",
  "#34d399",
  "#3b82f6",
  "#a78bfa",
];

const Theme = () => {
  const {
    primaryColor,
    backgroundColor,
    textColor,
    setPrimaryColor,
    setBackgroundColor,
    setTextColor,
    resetTheme,
  } = useThemeStore();
  const { selectedTemplateId } = useTemplateStore();

  const ColorPicker = ({ label, value, onChange }) => (
    <div className="mb-4">
      <label className="block font-semibold text-sm text-gray-700">
        {label}
      </label>
      <div className="flex flex-wrap items-center gap-2 mt-1">
        {presetColors.map((color) => (
          <button
            key={color}
            title={color}
            style={{
              background: color,
              border:
                value === color ? "2px solid #29354d" : "1px solid #e5e7eb",
              width: 30,
              height: 30,
              borderRadius: "50%",
            }}
            onClick={(e) => {
              e.preventDefault();
              onChange(color);
            }}
          />
        ))}
        <input
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-8 h-8 rounded cursor-pointer"
        />
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="border rounded p-1 text-xs w-20 text-gray-700"
          placeholder="#000000"
        />
      </div>
      <div className="text-xs text-gray-500 mt-1">Color code: {value}</div>
    </div>
  );

  return (
    <div className="p-4 bg-white rounded-xl shadow-md max-w-sm space-y-4">
      <h2 className="text-lg font-bold text-[#29354d]">Theme Settings</h2>

      <ColorPicker
        label="Primary Color"
        value={primaryColor}
        onChange={setPrimaryColor}
      />
      {/* <ColorPicker
        label="Background Color"
        value={backgroundColor}
        onChange={setBackgroundColor}
      /> */}
      <ColorPicker
        label="Text Color"
        value={textColor}
        onChange={setTextColor}
      />

      <div
        className="rounded-lg p-3 border"
        style={{
          background: backgroundColor,
          color: textColor,
          borderColor: primaryColor,
        }}
      >
        <span className="font-semibold" style={{ color: primaryColor }}>
          Preview:
        </span>{" "}
        Resume Outlook
      </div>

      <button
        onClick={() => resetTheme(selectedTemplateId)}
        className="w-full py-2 px-4 rounded bg-gray-200 hover:bg-gray-300 text-black font-medium transition"
      >
        Reset to Template Theme
      </button>
    </div>
  );
};

export default Theme;
