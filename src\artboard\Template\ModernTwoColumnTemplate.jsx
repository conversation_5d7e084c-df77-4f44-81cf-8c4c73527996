import React, { useEffect } from "react";
import { useAuthStore } from "../../store/authStore";
import { useResumeStore } from "../../store/useResumeDetailStore";
import { useThemeStore } from "../../store/themeStore";
import { useTypographyStore } from "../../store/themeTypographyStore";
import { FaLink, FaLocationArrow, FaMailBulk, FaPhoneAlt } from "react-icons/fa";

const ModernTwoColumnTemplate = ({ exportMode = false, themeOverrides = {} }) => {
  const { user, fetchCurrentUser } = useAuthStore();
  const { resume, fetchResume } = useResumeStore();

  const {
    primaryColor: storePrimary,
    backgroundColor: storeBackground,
    textColor: storeText,
  } = useThemeStore();

  const {
    fontFamily,
    fontVariant,
    fontSize,
    lineHeight,
    hideIcons,
    underlineLinks,
  } = useTypographyStore();

  const {
    primaryColor = storePrimary,
    backgroundColor = exportMode ? "#ffffff" : storeBackground,
    textColor = exportMode ? "#29354d" : storeText,
  } = themeOverrides;

  const variantStyle = {};
  if (fontVariant === "italic") {
    variantStyle.fontStyle = "italic";
    variantStyle.fontWeight = 400;
  } else if (!isNaN(fontVariant)) {
    variantStyle.fontWeight = fontVariant;
  }

  useEffect(() => {
    const linkId = "dynamic-google-font";
    let link = document.getElementById(linkId);
    if (link) link.remove();

    const formattedFont = fontFamily.replace(/ /g, "+");
    const fontWeight = isNaN(fontVariant)
      ? "400"
      : fontVariant;

    const newLink = document.createElement("link");
    newLink.id = linkId;
    newLink.rel = "stylesheet";
    newLink.href = `https://fonts.googleapis.com/css2?family=${formattedFont}:wght@${fontWeight}&display=swap`;
    document.head.appendChild(newLink);
  }, [fontFamily, fontVariant]);

  useEffect(() => {
    if (!user) fetchCurrentUser();
  }, []);

  useEffect(() => {
    if (user?.resumeId && !resume) fetchResume(user.resumeId);
  }, [user]);

  if (!resume || !user)
    return exportMode ? null : (
      <p className="text-center mt-20">Loading resume...</p>
    );

  const {
    Title,
    Email,
    Headline,
    Phone,
    Location,
    Website,
    ProfilePic,
    Profiles = [],
    Experience = [],
    Education = [],
    Skills = [],
    Languages = [],
    Certifications = [],
    Awards = [],
    Projects = [],
    Publications = [],
    Volunteering = [],
    References = [],
    Interests = [],
    summery = "",
  } = resume;

  const { name } = user;

  return (
    <div
      className="a4-container px-10 py-6 grid grid-cols-3 gap-6"
      style={{
        backgroundColor,
        color: textColor,
        fontFamily: `"${fontFamily}", sans-serif`,
        fontSize,
        lineHeight,
        ...variantStyle,
      }}
    >
      {/* Sidebar */}
      <aside className="col-span-1 space-y-6">
        {ProfilePic && (
          <img
            src={ProfilePic}
            alt="Profile"
            className="w-24 h-24 rounded-full object-cover mx-auto"
          />
        )}
        <h1
          className="text-xl font-bold text-center"
          style={{ color: primaryColor }}
        >
          {!hideIcons && "👤 "}
          {name}
        </h1>
        {Headline && (
          <p className="text-center italic text-xs" style={variantStyle}>
            {Headline}
          </p>
        )}

        <div className="text-xs space-y-1">
          {Email && (
            <p className="flex items-center gap-1">
              {!hideIcons && <FaMailBulk />}
              {Email}
            </p>
          )}
          {Phone && (
            <p className="flex items-center gap-1">
              {!hideIcons && <FaPhoneAlt />}
              {Phone}
            </p>
          )}
          {Location && (
            <p className="flex items-center gap-1">
              {!hideIcons && <FaLocationArrow />}
              {Location}
            </p>
          )}
          {Website && (
            <p className="flex items-center gap-1">
              {!hideIcons && <FaLink />}
              <a
                href={Website}
                style={{
                  textDecoration: underlineLinks ? "underline" : "none",
                  color: primaryColor,
                }}
              >
                {Website}
              </a>
            </p>
          )}
        </div>

        {Skills.length > 0 && (
          <Section title="Skills" primaryColor={primaryColor}>
            <ul className="list-disc pl-4 text-xs">
              {Skills.map((s) => (
                <li key={s._id}>{s.Skill}</li>
              ))}
            </ul>
          </Section>
        )}

        {Languages.length > 0 && (
          <Section title="Languages" primaryColor={primaryColor}>
            <ul className="list-disc pl-4 text-xs">
              {Languages.map((l) => (
                <li key={l._id}>
                  {l.Name} - {l.Proficiency}
                </li>
              ))}
            </ul>
          </Section>
        )}

        {Interests.length > 0 && (
          <Section title="Interests" primaryColor={primaryColor}>
            <ul className="list-disc pl-4 text-xs">
              {Interests.map((i, idx) => (
                <li key={idx}>{i.Interest || i}</li>
              ))}
            </ul>
          </Section>
        )}
      </aside>

      {/* Main Content */}
      <main className="col-span-2 space-y-4">
        {summery && (
          <Section title="Summary" primaryColor={primaryColor}>
            <p>{summery}</p>
          </Section>
        )}

        {Experience.length > 0 && (
          <Section title="Experience" primaryColor={primaryColor}>
            {Experience.map((exp) => (
              <Item
                key={exp._id}
                title={`${exp.Position} at ${exp.Company}`}
                subtitle={`${formatDate(exp.StartDate)} - ${formatDate(
                  exp.EndDate
                )} | ${exp.Location}`}
                description={exp.Description}
              />
            ))}
          </Section>
        )}

        {Projects.length > 0 && (
          <Section title="Projects" primaryColor={primaryColor}>
            {Projects.map((proj) => (
              <Item
                key={proj._id}
                title={proj.Title}
                subtitle={proj.Link}
                description={proj.Description}
                extra={proj.Technologies?.join(", ")}
              />
            ))}
          </Section>
        )}

        {Education.length > 0 && (
          <Section title="Education" primaryColor={primaryColor}>
            {Education.map((edu) => (
              <Item
                key={edu._id}
                title={`${edu.Degree} at ${edu.Institution}`}
                subtitle={`${formatDate(edu.StartDate)} - ${formatDate(
                  edu.EndDate
                )} | ${edu.Location}`}
              />
            ))}
          </Section>
        )}

        {Certifications.length > 0 && (
          <Section title="Certifications" primaryColor={primaryColor}>
            {Certifications.map((cert) => (
              <Item
                key={cert._id}
                title={cert.Title}
                subtitle={`${cert.Issuer} - ${formatDate(cert.Date)}`}
                description={cert.Description}
              />
            ))}
          </Section>
        )}

        {Awards.length > 0 && (
          <Section title="Awards" primaryColor={primaryColor}>
            {Awards.map((award) => (
              <Item
                key={award._id}
                title={award.Title}
                subtitle={`${award.Issuer} - ${formatDate(award.Date)}`}
              />
            ))}
          </Section>
        )}

        {Publications.length > 0 && (
          <Section title="Publications" primaryColor={primaryColor}>
            {Publications.map((pub) => (
              <Item
                key={pub._id}
                title={pub.Title}
                subtitle={`${pub.Publisher} - ${formatDate(pub.Date)}`}
                extra={pub.Website}
              />
            ))}
          </Section>
        )}

        {Volunteering.length > 0 && (
          <Section title="Volunteering" primaryColor={primaryColor}>
            {Volunteering.map((v) => (
              <Item
                key={v._id}
                title={`${v.Position} at ${v.Organization}`}
                subtitle={`${formatDate(v.StartDate)} – ${formatDate(
                  v.EndDate
                )} | ${v.Location}`}
                description={v.Description}
              />
            ))}
          </Section>
        )}

        {References.length > 0 && (
          <Section title="References" primaryColor={primaryColor}>
            {References.map((r) => (
              <Item
                key={r._id}
                title={r.Name}
                subtitle={`${r.Position} at ${r.Company}`}
                extra={`${r.Email} | ${r.Phone}`}
              />
            ))}
          </Section>
        )}

        {Profiles.length > 0 && (
          <Section title="Profiles" primaryColor={primaryColor}>
            <ul className="list-disc pl-4 text-xs">
              {Profiles.map((p, idx) => (
                <li key={p._id || idx}>
                  {p.Network}:{" "}
                  <a
                    href={p.ProfileLink}
                    style={{
                      textDecoration: underlineLinks ? "underline" : "none",
                      color: primaryColor,
                    }}
                    target="_blank"
                    rel="noreferrer"
                  >
                    {p.Username}
                  </a>
                </li>
              ))}
            </ul>
          </Section>
        )}
      </main>
    </div>
  );
};

const Section = ({ title, children, primaryColor }) => (
  <div className="mb-3">
    <h2
      className="font-bold text-sm border-b mb-1 pb-0.5 uppercase tracking-wide"
      style={{ borderColor: primaryColor }}
    >
      {title}
    </h2>
    {children}
  </div>
);

const Item = ({ title, subtitle, description, extra }) => (
  <div className="mb-2">
    <p className="text-sm font-semibold">{title}</p>
    {subtitle && <p className="text-xs italic">{subtitle}</p>}
    {description && <p className="text-xs">{description}</p>}
    {extra && <p className="text-xs text-gray-500">{extra}</p>}
  </div>
);

const formatDate = (date) => {
  if (!date) return "Present";
  try {
    return new Date(date).toLocaleDateString("en-IN", {
      month: "short",
      year: "numeric",
    });
  } catch {
    return "N/A";
  }
};

export default ModernTwoColumnTemplate;
