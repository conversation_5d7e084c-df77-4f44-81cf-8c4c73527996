import React from "react";

const Interests = ({
  interests = [],
  title = "Interests",
  sectionStyle = "",
  titleStyle = "",
  listStyle = "",
  itemStyle = "",
}) => {
  if (!interests || interests.length === 0) return null;

  return (
    <section className={sectionStyle}>
      {title && (
        <h3 className={`text-lg font-bold mb-2 ${titleStyle}`}>
          {title}
        </h3>
      )}
      <ul className={`list-disc list-inside ${listStyle}`}>
        {interests.map((interest, idx) => (
          <li key={idx} className={itemStyle}>
            {interest}
          </li>
        ))}
      </ul>
    </section>
  );
};

export default Interests;
