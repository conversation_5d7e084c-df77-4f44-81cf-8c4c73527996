import { create } from "zustand";
import axios from "axios";
import {
    ADDINFO_ENDPOINTS,
    GETINFO_ENDPOINT,
    DELETE_INFO,
} from "../lib/constants";

export const usePublicationStore = create((set, get) => ({
    publications: [],
    isLoading: false,
    error: null,

    // Fetch all publications
    fetchPublications: async () => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.get(GETINFO_ENDPOINT, {
                withCredentials: true,
            });
            const user = res.data.user;
            console.log("Fetched Publications:", user?.Publications);

            set({
                publications: user?.Publications || [],
                isLoading: false,
            });
        } catch (error) {
            console.error("Failed to fetch publications:", error);
            set({ isLoading: false, error: error.message || "Error fetching data" });
        }
    },

    // Add a publication and then refetch list
    addPublications: async (data) => {
        try {
            await axios.post(ADDINFO_ENDPOINTS.PUBLICATIONS, data, {
                withCredentials: true,
            });

            // Refetch after successful addition
            await get().fetchPublications();
        } catch (error) {
            console.error("Failed to add publication:", error.response?.data || error.message);
        }
    },

    deletePublication: async (publication) => {
        try {
            await axios.delete(DELETE_INFO.PUBLICATIONS(publication._id), {
                withCredentials: true,
            });

            // Refetch after successful deletion
            await get().fetchPublications();
        } catch (error) {
            console.error("Failed to delete publication:", error.response?.data || error.message);
        }
    },

    // Stub (can be implemented if needed)
    updatePublications: () => { },
    deletePublications: () => { },
}));
