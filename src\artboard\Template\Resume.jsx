// Resume.jsx
import React from "react";

const Resume = ({ data }) => {
  const defaultData = {
    header: {
      name: "<PERSON>",
      title: "Software Developer",
      phone: "************",
      email: "<EMAIL>",
      address: "123 Anywhere St., Any City",
      website: "www.reallygreatsite.com",
    },
    summary:
      "Highly skilled and detail-oriented software developer with 5 years of experience designing, developing, and deploying enterprise-level applications.",
    skills: [
      "Strong problem solving",
      "Analytical skills",
      "Communication",
      "Leadership",
    ],
    experience: [
      {
        company: "Fradel and Spies Co",
        role: "Software Developer",
        period: "2020 – Present",
        duties: [
          "Collaborated with cross-functional teams",
          "Conducted code reviews",
        ],
      },
    ],
    education: [
      {
        degree: "Bachelor of Science in Computer Science",
        institution: "Studio School · 2015–2018",
      },
    ],
    certifications: [
      {
        name: "Certified Scrum Developer",
        issuer: "Licoria & Co · 2020–2021",
      },
    ],
    volunteering: [
      {
        organization: "Open Source Contributions",
        role: "Contributor",
        period: "2019 – Present",
        details: [
          "Contributed to several open-source projects on GitHub.",
          "Helped maintain community documentation."
        ]
      }
    ],
    references: [
      {
        name: "<PERSON>",
        contact: "<EMAIL>",
        relation: "Project Manager at ABC Corp"
      }
    ]
  };

  const resumeData = data || defaultData;

  return (
    <div className="bg-white w-[210mm] h-[297mm] mx-auto shadow-xl p-[20mm] print:p-[20mm] print:shadow-none text-gray-800">
      {/* Header */}
      <div className="flex justify-between items-start border-b-2 pb-6">
        <div>
          <h1 className="text-4xl font-bold" contentEditable>{resumeData.header.name}</h1>
          <p className="text-base text-gray-500 mt-1" contentEditable>{resumeData.header.title}</p>
        </div>
        <div className="text-right text-sm leading-6 space-y-1" contentEditable>
          <p>📞 {resumeData.header.phone}</p>
          <p>📧 {resumeData.header.email}</p>
          <p>📍 {resumeData.header.address}</p>
          <p>🌐 {resumeData.header.website}</p>
        </div>
      </div>

      {/* Summary */}
      <section className="mt-10">
        <h2 className="uppercase text-sm font-bold text-gray-600 border-b mb-3 pb-1" contentEditable>Summary</h2>
        <p className="text-sm leading-relaxed" contentEditable>{resumeData.summary}</p>
      </section>

      {/* Skills */}
      <section className="mt-8">
        <h2 className="uppercase text-sm font-bold text-gray-600 border-b mb-3 pb-1" contentEditable>Skills</h2>
        <ul className="flex flex-wrap gap-3 text-sm" contentEditable>
          {resumeData.skills.map((skill, index) => (
            <li key={index} className="bg-gray-100 px-3 py-1 rounded-md">{skill}</li>
          ))}
        </ul>
      </section>

      {/* Experience */}
      <section className="mt-8">
        <h2 className="uppercase text-sm font-bold text-gray-600 border-b mb-3 pb-1" contentEditable>Work Experience</h2>
        {resumeData.experience.map((job, index) => (
          <div key={index} className="mb-6">
            <h3 className="text-base font-semibold" contentEditable>{job.company}</h3>
            <p className="text-sm text-gray-600 mb-1" contentEditable>{job.role} · {job.period}</p>
            <ul className="list-disc pl-5 text-sm space-y-1" contentEditable>
              {job.duties.map((duty, idx) => <li key={idx}>{duty}</li>)}
            </ul>
          </div>
        ))}
      </section>

      {/* Education */}
      <section className="mt-8">
        <h2 className="uppercase text-sm font-bold text-gray-600 border-b mb-3 pb-1" contentEditable>Education</h2>
        {resumeData.education.map((edu, index) => (
          <div key={index} className="flex justify-between text-sm mb-3">
            <div contentEditable>{edu.degree}</div>
            <div contentEditable>{edu.institution}</div>
          </div>
        ))}
      </section>

      {/* Certifications */}
      <section className="mt-8">
        <h2 className="uppercase text-sm font-bold text-gray-600 border-b mb-3 pb-1" contentEditable>Certifications</h2>
        {resumeData.certifications.map((cert, index) => (
          <div key={index} className="flex justify-between text-sm mb-3">
            <div contentEditable>{cert.name}</div>
            <div contentEditable>{cert.issuer}</div>
          </div>
        ))}
      </section>

      {/* Volunteering */}
      <section className="mt-8">
        <h2 className="uppercase text-sm font-bold text-gray-600 border-b mb-3 pb-1" contentEditable>Volunteering</h2>
        {resumeData.volunteering.map((item, index) => (
          <div key={index} className="mb-4">
            <h3 className="text-base font-semibold" contentEditable>{item.organization}</h3>
            <p className="text-sm text-gray-600 mb-1" contentEditable>{item.role} · {item.period}</p>
            <ul className="list-disc pl-5 text-sm space-y-1" contentEditable>
              {item.details.map((line, idx) => <li key={idx}>{line}</li>)}
            </ul>
          </div>
        ))}
      </section>

      {/* References */}
      <section className="mt-8">
        <h2 className="uppercase text-sm font-bold text-gray-600 border-b mb-3 pb-1" contentEditable>References</h2>
        {resumeData.references.map((ref, index) => (
          <div key={index} className="text-sm mb-2" contentEditable>
            <p className="font-semibold">{ref.name}</p>
            <p>{ref.contact}</p>
            <p className="text-gray-600 italic">{ref.relation}</p>
          </div>
        ))}
      </section>
    </div>
  );
};

export default Resume;
