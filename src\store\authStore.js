import { create } from "zustand";
import axios from "axios";
import { AUTH_ENDPOINTS } from "../lib/constants";
import { useResumeStore } from "./useResumeDetailStore";

export const useAuthStore = create((set, get) => ({
    user: null,
    isAuthenticated: false,
    isLoading: false,
    isHydrated: false,
    error: null,

    register: async ({ username, email, password }) => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.post(
                AUTH_ENDPOINTS.REGISTER,
                { name: username, email, password },
                { withCredentials: true }
            );
            set({ user: res.data.user, isAuthenticated: true, isLoading: false });

            if (res.data.user?.resumeId) {
                useResumeStore.getState().fetchResume(res.data.user.resumeId);
            }
        } catch (error) {
            const errorMsg = error?.response?.data?.error || "Registration failed";
            set({ error: errorMsg, isLoading: false });
            throw new Error(errorMsg);
        }
    },

    verifyOtp: async ({ email, otp }) => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.post(
                AUTH_ENDPOINTS.VERIFY_OTP,
                { email, otp },
                { withCredentials: true }
            );
            await get().fetchCurrentUser();
            set({ isAuthenticated: true, isLoading: false });
            return res.data.message;
        } catch (error) {
            const errorMsg =
                error?.response?.data?.message || "OTP verification failed";
            set({ error: errorMsg, isLoading: false });
            throw new Error(errorMsg);
        }
    },

    resendOtp: async ({ email }) => {
        set({ isLoading: true, error: null });

        try {
            const res = await axios.post(
                AUTH_ENDPOINTS.RESEND_OTP,
                { email }, // ✅ Destructured properly now
                { withCredentials: true }
            );

            set({ isLoading: false });
            return res.data.message;
        } catch (error) {
            const errorMsg = error?.response?.data?.error || "Failed to resend OTP.";
            set({ isLoading: false, error: errorMsg });
            throw new Error(errorMsg);
        }
    },      
    login: async ({ email, password }) => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.post(
                AUTH_ENDPOINTS.LOGIN,
                { email, password },
                { withCredentials: true }
            );

            const { user, message } = res.data;

            if (user) {
                set({ user, isAuthenticated: true, isLoading: false });
                if (user.resumeId) {
                    useResumeStore.getState().fetchResume(user.resumeId);
                }
                return { message };
            } else {
                throw new Error("Invalid login response");
            }
        } catch (error) {
            set({ isLoading: false });
            throw error;
        }
    },

    fetchCurrentUser: async () => {
        const state = get();
        if (state.isHydrated) return;

        set({ isLoading: true });
        try {
            const res = await axios.get(AUTH_ENDPOINTS.CURRENT_USER, {
                withCredentials: true,
            });

            const user = res.data.user;

            set({
                user,
                isAuthenticated: true,
                isLoading: false,
                isHydrated: true,
            });

            if (user?.resumeId) {
                useResumeStore.getState().fetchResume(user.resumeId);
            }
        } catch {
            set({
                isLoading: false,
                isAuthenticated: false,
                user: null,
                isHydrated: true,
            });
        }
    },

    updateUser: async (userData) => {
        try {
            const res = await axios.put(AUTH_ENDPOINTS.UPDATE_USER, userData, {
                withCredentials: true,
            });
            set({ user: res.data.user });
            return res.data.user;
        } catch (error) {
            console.error(error);
            throw error;
        }
    },

    logout: async () => {
        try {
            await axios.post(AUTH_ENDPOINTS.LOGOUT, {}, { withCredentials: true });
        } catch (error) {
            console.error(error);
        } finally {
            set({ user: null, isAuthenticated: false, isHydrated: true });
            useResumeStore.getState().clearResume();
        }
    },
}));
