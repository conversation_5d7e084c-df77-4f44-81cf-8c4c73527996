import React, { useState, useEffect } from "react";
import HomepageLayout from "../../components/Layout/HomepageLayout";
import { useAuthStore } from "../../store/authStore";
import { toast } from "react-toastify";
import { useNavigate, useLocation } from "react-router-dom";
import "react-toastify/dist/ReactToastify.css";
import sideImage from "../../assets/loginSideImg.jpg";

const Login = () => {
  const { login, isLoading, isAuthenticated } = useAuthStore();
  const navigate = useNavigate();
  const location = useLocation();
  const redirectPath = "/dashboard"; // Always redirect to /dashboard after login

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  const [googleEmail, setGoogleEmail] = useState("");
  const [showGoogleModal, setShowGoogleModal] = useState(false);
  const [linkedinEmail, setLinkedinEmail] = useState("");
  const [showLinkedinModal, setShowLinkedinModal] = useState(false);
  const [githubEmail, setGithubEmail] = useState("");
  const [showGithubModal, setShowGithubModal] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      navigate(redirectPath, { replace: true });
    }
  }, [isAuthenticated, navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!email.trim()) return toast.error("Email is required");
    if (!password.trim()) return toast.error("Password is required");

    try {
      const { message } = await login({ email, password });
      toast.success(message || "Logged in successfully!");
      setEmail("");
      setPassword("");
    } catch (err) {
      const message =
        err?.response?.data?.error ||
        err?.message ||
        "Invalid email or password";
      toast.error(message);
    }
  };

  const handleSocialLogin = (provider, email) => {
    if (!email) return toast.error("Please enter your email");
    toast.success(`${provider} sign-in for ${email}`);
  };

  return (
    <HomepageLayout>
      <main className="flex w-screen min-h-screen bg-white font-poppins">
        <div className="flex flex-col justify-center min-h-screen w-full md:w-[35vw] max-w-[35vw] md:px-10 px-4 py-10 bg-white z-10 shadow-lg">
          <div className="flex flex-col items-center mb-8">
            <img
              src="/images/icons8-resume-50.png"
              alt="Logo"
              className="w-12 h-12 rounded-lg shadow-md bg-white"
            />
            <span className="text-2xl font-bold text-[#29354d] tracking-wide">
              ResumeBuilder
            </span>
          </div>

          <div className="rounded-xl bg-white shadow-md p-8 w-full animate-fadeInUp">
            <h1 className="text-2xl font-semibold mb-2">Welcome Back</h1>
            <p className="text-gray-500 mb-6 text-sm">
              Please enter your credentials to log in
            </p>

            <form
              className="space-y-5"
              onSubmit={handleSubmit}
              autoComplete="off"
            >
              <div>
                <label
                  htmlFor="email"
                  className="block mb-1 font-medium text-gray-700"
                >
                  Email
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                    <i className="fas fa-envelope" />
                  </span>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    required
                    className="w-full pl-10 pr-3 py-3 rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:bg-white bg-gray-50 outline-none transition-all text-base"
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="password"
                  className="block mb-1 font-medium text-gray-700"
                >
                  Password
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                    <i className="fas fa-lock" />
                  </span>
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    required
                    className="w-full pl-10 pr-10 py-3 rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:bg-white bg-gray-50 outline-none transition-all text-base"
                  />
                  <span
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 cursor-pointer hover:text-blue-500"
                    onClick={() => setShowPassword((prev) => !prev)}
                  >
                    <i
                      className={
                        showPassword ? "fas fa-eye-slash" : "fas fa-eye"
                      }
                    />
                  </span>
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className={`w-full py-3 rounded-lg font-semibold flex items-center justify-center ${
                  isLoading
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-[#29354d] text-[#fcc250] hover:bg-[#fcc250] hover:text-[#29354d]"
                } transition`}
              >
                {isLoading ? (
                  <>
                    <svg
                      className="animate-spin h-5 w-5 text-white mr-2"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                      ></path>
                    </svg>
                    Logging in...
                  </>
                ) : (
                  <>
                    Login <i className="fas fa-arrow-right ml-2" />
                  </>
                )}
              </button>

              <div className="text-center text-gray-700 mt-2 text-sm">
                Don&apos;t have an account?{" "}
                <a
                  href="/register"
                  className="text-blue-600 font-semibold hover:underline"
                >
                  Create an account
                </a>
              </div>
            </form>

            <div className="flex items-center my-6">
              <div className="flex-1 border-t border-gray-200" />
              <span className="mx-4 text-gray-400 text-sm">
                or sign in with
              </span>
              <div className="flex-1 border-t border-gray-200" />
            </div>

            <div className="flex justify-center gap-4">
              {["Google", "GitHub", "LinkedIn"].map((provider) => (
                <button
                  key={provider}
                  title={`Sign in with ${provider}`}
                  className="flex items-center justify-center w-14 h-14 rounded-full border-2 border-gray-200 bg-white shadow hover:scale-105 transition"
                  onClick={(e) => {
                    e.preventDefault();
                    provider === "Google"
                      ? setShowGoogleModal(true)
                      : provider === "GitHub"
                      ? setShowGithubModal(true)
                      : setShowLinkedinModal(true);
                  }}
                >
                  <img
                    src={`/images/${provider.toLowerCase()}.png`}
                    alt={provider}
                    className="w-7 h-7"
                  />
                </button>
              ))}
            </div>

            {/* Modals */}
            {showGoogleModal && (
              <SocialModal
                title="Google"
                email={googleEmail}
                setEmail={setGoogleEmail}
                onContinue={(email) => {
                  handleSocialLogin("Google", email);
                  setShowGoogleModal(false);
                }}
                onClose={() => setShowGoogleModal(false)}
              />
            )}
            {showGithubModal && (
              <SocialModal
                title="GitHub"
                email={githubEmail}
                setEmail={setGithubEmail}
                onContinue={(email) => {
                  handleSocialLogin("GitHub", email);
                  setShowGithubModal(false);
                }}
                onClose={() => setShowGithubModal(false)}
              />
            )}
            {showLinkedinModal && (
              <SocialModal
                title="LinkedIn"
                email={linkedinEmail}
                setEmail={setLinkedinEmail}
                onContinue={(email) => {
                  handleSocialLogin("LinkedIn", email);
                  setShowLinkedinModal(false);
                }}
                onClose={() => setShowLinkedinModal(false)}
              />
            )}
          </div>
        </div>

        <div className="hidden md:flex w-[65vw] relative min-h-screen">
          <img
            src={sideImage}
            alt="Login Side"
            className="absolute inset-0 w-full h-full object-cover brightness-75"
          />
        </div>
      </main>

      <style>{`
        @keyframes fadeInUp {
          from { opacity: 0; transform: translateY(30px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeInUp {
          animation: fadeInUp 0.6s ease-out both;
        }
      `}</style>
    </HomepageLayout>
  );
};

export default Login;

// Modal for Social Login
const SocialModal = ({ title, email, setEmail, onContinue, onClose }) => (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-[#181818] text-white rounded-xl p-8 shadow-xl w-full max-w-xs relative flex flex-col items-center animate-fadeInUp">
      <div className="flex items-center gap-2 mb-4">
        <img
          src={`/images/${title.toLowerCase()}.png`}
          alt={title}
          className="w-8 h-8"
        />
        <span className="font-semibold text-lg">Sign in with {title}</span>
      </div>
      <label htmlFor={`${title}-email`} className="mb-1 text-white">
        Email
      </label>
      <input
        id={`${title}-email`}
        className="w-full mb-4 px-3 py-2 rounded-lg bg-[#232323] border border-gray-700 text-white focus:border-blue-400 outline-none"
        type="email"
        placeholder={`Enter your ${title} email`}
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        autoFocus
      />
      <button
        className="w-full bg-[#fcc250] text-[#29354d] font-semibold py-2 rounded-lg hover:bg-[#29354d] hover:text-[#fcc250] transition"
        onClick={() => onContinue(email)}
      >
        Continue
      </button>
      <button
        className="absolute top-2 right-3 text-2xl text-white hover:text-red-500"
        onClick={onClose}
        title="Close"
      >
        ×
      </button>
    </div>
  </div>
);
