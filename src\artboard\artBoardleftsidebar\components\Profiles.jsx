import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { FaShareAlt, FaBars, FaPlus, FaTimes } from "react-icons/fa";

import { useResumeStore } from "../../../store/useResumeDetailStore";
import { useAddInfoStore } from "../../../store/addInfoStore";
import { useUpdateInfoStore } from "../../../store/updateInfoStore";
import { useDeleteInfoStore } from "../../../store/deleteInfoStore";

import DataCard from "../../Components/DataCard";
import AddProfileModal from "../../Components/AddProfileModal";
import toast from "react-hot-toast";

const Profiles = () => {
  const { resumeId } = useParams();
  const { resume, fetchResume, loading } = useResumeStore();
  const { addInfo } = useAddInfoStore();
  const { updateInfo } = useUpdateInfoStore();
  const { deleteInfo } = useDeleteInfoStore();

  const profiles = resume?.Profiles ?? [];

  const [modalOpen, setModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(null);

  useEffect(() => {
    if (resumeId) fetchResume(resumeId);
  }, [resumeId]);

  const handleDeleteConfirmed = async () => {
    if (!resumeId || !confirmDelete) return;
    try {
      await deleteInfo({
        resumeId,
        section: "profile",
        entryId: confirmDelete._id,
      });
      toast.success("Profile deleted.");
      await fetchResume(resumeId);
    } catch (err) {
      toast.error("Failed to delete profile.");
    } finally {
      setConfirmDelete(null);
    }
  };

  const handleAddOrUpdate = async (payload, entryId = null) => {
    if (!resumeId) return;

    try {
      if (entryId) {
        await updateInfo({
          resumeId,
          section: "profile",
          entryId,
          updatedData: payload,
        });
        toast.success("Profile updated!");
      } else {
        await addInfo({
          resumeId,
          section: "profile",
          newData: payload,
        });
        toast.success("Profile added!");
      }

      await fetchResume(resumeId);
      setModalOpen(false);
      setEditData(null);
    } catch (err) {
      toast.error("Failed to save profile.");
    }
  };

  return (
    <div className="bg-white min-h-screen py-10 px-4 md:px-8 text-[#29354d] font-sans">
      {/* Header */}
      <header className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <FaShareAlt className="text-[#29354d]" />
          <h2 className="text-2xl font-extrabold">Profiles</h2>
        </div>
        <FaBars className="text-[#29354d]" />
      </header>

      {/* Add Button */}
      <button
        type="button"
        onClick={() => {
          setEditData(null);
          setModalOpen(true);
        }}
        className="cursor-pointer border-2 border-dashed border-gray-400 rounded-lg py-5 px-8 flex items-center justify-center bg-gray-200 hover:bg-gray-300 text-lg font-medium mb-8 w-full md:w-auto"
      >
        <FaPlus className="mr-2" /> Add a new profile
      </button>

      {/* Profile List */}
      {loading ? (
        <p className="text-gray-500">Loading…</p>
      ) : profiles.length === 0 ? (
        <p className="text-gray-500">No profiles added yet.</p>
      ) : (
        <div className="flex flex-wrap gap-4">
          {profiles.map((profile) => (
            <div key={profile._id} className="w-fit min-w-[250px]">
              <DataCard
                title={profile.Network}
                subtitle={profile.Username}
                description={profile.Url || ""}
                onEdit={() => {
                  setEditData(profile);
                  setModalOpen(true);
                }}
                onDelete={() => setConfirmDelete(profile)}
              />
            </div>
          ))}
        </div>
      )}

      {/* Add / Edit Modal */}
      {modalOpen && (
        <AddProfileModal
          open={modalOpen}
          initial={editData}
          onClose={() => {
            setModalOpen(false);
            setEditData(null);
          }}
          onSave={handleAddOrUpdate}
        />
      )}

      {/* Delete Confirmation Modal */}
      {confirmDelete && (
        <div className="fixed inset-0 z-50 bg-black/60 flex items-center justify-center px-4">
          <div className="bg-white max-w-md w-full p-6 rounded-lg shadow-xl text-[#29354d]">
            <button
              onClick={() => setConfirmDelete(null)}
              className="absolute top-4 right-4 text-gray-500 hover:text-black"
            >
              <FaTimes size={20} />
            </button>
            <h3 className="text-lg font-bold mb-2">Delete Profile</h3>
            <p className="text-sm mb-4">
              Are you sure you want to delete the profile from{" "}
              <strong>{confirmDelete.Network}</strong>?
            </p>

            <div className="border p-3 rounded bg-gray-50 text-sm mb-4">
              <p>
                <strong>Username:</strong> {confirmDelete.Username}
              </p>
              {confirmDelete.Url && (
                <p>
                  <strong>URL:</strong>{" "}
                  <a
                    href={confirmDelete.Url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 underline"
                  >
                    {confirmDelete.Url}
                  </a>
                </p>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setConfirmDelete(null)}
                className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-sm"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirmed}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 text-sm"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Profiles;
