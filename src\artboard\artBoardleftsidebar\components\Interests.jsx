import React, { useState, useEffect } from "react";
import { FaGamepad, FaPlus, FaBars } from "react-icons/fa";
import { useResumeStore } from "../../../store/useResumeDetailStore";
import { useAddInfoStore } from "../../../store/addInfoStore";
import { useDeleteInfoStore } from "../../../store/deleteInfoStore";

const Interests = () => {
  const { resume, fetchResume, isLoading } = useResumeStore();
  const addInfo = useAddInfoStore((state) => state.addInfo);
  const deleteInfo = useDeleteInfoStore((state) => state.deleteInfo);

  const interests = resume?.Interests || []; // ✅ Fixed casing
  const resumeId = resume?._id;

  const [showModal, setShowModal] = useState(false);
  const [interestInput, setInterestInput] = useState("");

  useEffect(() => {
    if (resumeId) fetchResume(resumeId); // ✅ Ensure ID is passed
  }, [resumeId]);

  const handleAdd = async () => {
    const trimmed = interestInput.trim();
    if (!trimmed || !resumeId) return;

    await addInfo({
      resumeId,
      section: "interests",
      newData: { Name: trimmed },
    });

    setInterestInput("");
    setShowModal(false);
    fetchResume(resumeId); // ✅ Refresh data
  };

  const handleDelete = async (id) => {
    if (!resumeId) return;

    const confirm = window.confirm("Delete this interest?");
    if (!confirm) return;

    await deleteInfo({
      resumeId,
      section: "interests",
      entryId: id,
    });

    fetchResume(resumeId); // ✅ Refresh
  };

  return (
    <div className="bg-white min-h-screen py-10 px-4 md:px-8 text-black font-sans">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <FaGamepad className="text-black" size={20} />
          <h2 className="text-2xl font-bold">Interests</h2>
        </div>
        <FaBars size={20} className="text-gray-400" />
      </div>

      {/* Add New */}
      <div
        onClick={() => setShowModal(true)}
        className="cursor-pointer border-2 border-dashed border-gray-400 rounded-lg py-4 px-6 flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-black text-base font-medium mb-8 transition"
      >
        <FaPlus className="mr-2" /> Add a new item
      </div>

      {/* Interests List */}
      <div className="bg-[#f2f0f0] border border-white/20 rounded-xl shadow-md p-6">
        <h3 className="text-lg font-semibold mb-4">Your Added Interests</h3>
        {isLoading ? (
          <p className="text-gray-500">Loading...</p>
        ) : interests.length === 0 ? (
          <p className="text-gray-500">No interests added yet.</p>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {interests.map((interest) => (
              <div
                key={interest._id}
                className="bg-white border border-gray-300 rounded-lg px-4 py-3 shadow-sm flex justify-between items-center"
              >
                <span className="text-black">{interest.Name}</span>
                <button
                  onClick={() => handleDelete(interest._id)}
                  className="text-red-500 hover:text-red-700 text-sm font-medium ml-2"
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center">
          <div className="bg-white p-8 rounded-xl w-full max-w-md shadow-2xl relative">
            <button
              onClick={() => setShowModal(false)}
              className="absolute top-4 right-4 text-2xl text-gray-400 hover:text-black"
            >
              ×
            </button>

            <h2 className="text-xl font-bold mb-6 flex items-center gap-2">
              <FaPlus /> Add Interest
            </h2>

            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleAdd();
              }}
              className="space-y-4"
            >
              <input
                type="text"
                placeholder="e.g. Gaming, Reading..."
                className="w-full bg-gray-50 border border-gray-400 rounded-lg px-4 py-2 text-black"
                value={interestInput}
                onChange={(e) => setInterestInput(e.target.value)}
              />
              <button
                type="submit"
                className="bg-black text-white px-6 py-2 rounded-lg float-right hover:bg-gray-800 transition"
              >
                Create
              </button>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Interests;
