import React, { Fragment } from "react";

const sampleResume = {
  basics: {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    headline: "Software Engineer",
    location: "Bangalore, India",
    phone: "+91 9876543210",
    email: "<EMAIL>",
    url: { href: "https://abhi.dev", label: "Portfolio" },
    customFields: [
      { id: "1", name: "LinkedIn", value: "https://linkedin.com/in/abhikumar" },
      { id: "2", name: "GitHub", value: "https://github.com/abhiCoder" },
    ],
  },
  sections: {
    summary: {
      visible: true,
      name: "Summary",
      content: "<p>Results-driven Software Engineer with 3+ years of experience in full-stack development. Skilled in React, Node.js, and cloud technologies.</p>",
      id: "summary",
    },
    experience: {
      visible: true,
      name: "Experience",
      id: "experience",
      items: [
        {
          id: "exp1",
          company: "InnoTech",
          position: "Frontend Developer",
          date: "2022 - Present",
          location: "Remote",
          summary: '<p>Developed React dashboard, integrated APIs, collaborated with backend team.</p>',
        },
        {
          id: "exp2",
          company: "WebStart",
          position: "Intern",
          date: "2021 - 2022",
          location: "Bangalore",
          summary: "<p>Built internal tools using Node.js and MongoDB.</p>",
        },
      ],
    },
    education: {
      visible: true,
      name: "Education",
      id: "education",
      items: [
        {
          id: "edu1",
          institution: "NIT Trichy",
          studyType: "B.Tech",
          area: "Computer Science and Engineering",
          date: "2018 - 2022",
        },
      ],
    },
    volunteering: {
      visible: true,
      name: "Volunteering",
      id: "volunteering",
      items: [
        {
          id: "vol1",
          organization: "Code for Good",
          position: "Volunteer Developer",
          date: "2020 - 2021",
          summary: '<p>Contributed to open-source projects and mentored junior developers.</p>',
        },
      ],
    },
    references: {
      visible: true,
      name: "References",
      id: "references",
      items: [
        { id: "ref1", name: "John Doe", detail: "Senior Developer, InnoTech (<EMAIL>)" },
        { id: "ref2", name: "Jane Smith", detail: "Project Manager, WebStart (<EMAIL>)" },
      ],
    },
    publications: {
      visible: true,
      name: "Publications",
      id: "publications",
      items: [
        {
          id: "pub1",
          name: "Deep Learning in Web Applications",
          publisher: "International Journal of AI Research",
          date: "March 2023",
          summary: "Explored deep learning models for web platforms.",
          link: "https://example.com/publication1",
          linkLabel: "Read More",
        },
      ],
    },
    projects: {
      visible: true,
      name: "Projects",
      id: "projects",
      items: [
        {
          id: "proj1",
          name: "AI Chatbot",
          description: "AI-powered chatbot for customer support using Python and React.",
          link: "https://example.com/chatbot",
          linkLabel: "View Project",
        },
        {
          id: "proj2",
          name: "Portfolio Website",
          description: "Responsive portfolio built with React & Tailwind CSS.",
          link: "https://abhi.dev",
          linkLabel: "Visit Site",
        },
      ],
    },
    skills: {
      visible: true,
      name: "Skills",
      id: "skills",
      items: [
        { id: "sk1", name: "Frontend", description: "React, Redux, HTML5, CSS3, Tailwind CSS" },
        { id: "sk2", name: "Backend", description: "Node.js, Express, MongoDB, REST APIs" },
        { id: "sk3", name: "Cloud & DevOps", description: "AWS, Docker, CI/CD" },
        { id: "sk4", name: "Programming", description: "JavaScript, TypeScript, Python" },
      ],
    },
  },
};

const HeaderBar = ({ basics }) => (
  <header className="w-full bg-blue-900 text-white py-8 px-8 rounded-t-lg flex flex-col md:flex-row items-center mb-4 gap-6">
    {/* Profile photo left */}
    <div className="flex-shrink-0 w-24 h-24 rounded-full overflow-hidden bg-white border-4 border-blue-200 flex items-center justify-center">
      <img
        src="https://randomuser.me/api/portraits/men/32.jpg"
        alt="Profile"
        className="object-cover w-full h-full"
      />
    </div>
    {/* Name and contact right */}
    <div className="flex-1 flex flex-col items-center md:items-start">
      <h1 className="text-3xl font-bold">{basics.name}</h1>
      <p className="text-lg">{basics.headline}</p>
      <div className="flex flex-wrap justify-center md:justify-start gap-4 text-sm mt-2">
        <span>📍 {basics.location}</span>
        <span>📞 {basics.phone}</span>
        <span>✉️ <a href={`mailto:${basics.email}`} className="underline">{basics.email}</a></span>
        <span><a href={basics.url.href} className="underline">{basics.url.label}</a></span>
      </div>
      <div className="flex gap-4 mt-2">
        {basics.customFields.map(field => (
          <a key={field.id} href={field.value} target="_blank" rel="noreferrer" className="underline">{field.name}</a>
        ))}
      </div>
    </div>
  </header>
);

const SkillsBar = ({ section }) => (
  <section className="bg-blue-50 p-4 rounded mb-6 flex flex-wrap gap-6 justify-center">
    {section.items.map(skill => (
      <div key={skill.id} className="text-sm font-semibold text-blue-900">
        <span className="uppercase text-blue-700 mr-1">{skill.name}:</span>
        <span>{skill.description}</span>
      </div>
    ))}
  </section>
);

const Section = ({ section, children }) => {
  if (!section.visible || !section.items?.length) return null;
  return (
    <section className="mb-8">
      <h3 className="text-lg font-bold text-blue-900 uppercase border-b-2 border-blue-200 pb-1 mb-4">{section.name}</h3>
      <div className="space-y-4">
        {section.items.map(item => (
          <Fragment key={item.id}>{children(item)}</Fragment>
        ))}
      </div>
    </section>
  );
};

const Summary = ({ section }) => (
  section.visible && (
    <section className="mb-8">
      <h3 className="text-lg font-bold text-blue-900 uppercase border-b-2 border-blue-200 pb-1 mb-4">{section.name}</h3>
      <div dangerouslySetInnerHTML={{ __html: section.content }} className="text-gray-800 text-base leading-relaxed" />
    </section>
  )
);

const PublicationsSection = ({ title, publications }) => (
  <Section section={{ name: title, items: publications, visible: publications.length > 0 }}>
    {pub => (
      <div className="text-sm">
        <p className="font-semibold text-gray-900">{pub.name}</p>
        <p className="italic text-blue-800">{pub.publisher}</p>
        <p className="text-xs text-gray-600">{pub.date}</p>
        <p className="text-sm text-gray-700">{pub.summary}</p>
        {pub.link && <a href={pub.link} className="underline text-blue-700" target="_blank" rel="noreferrer">{pub.linkLabel}</a>}
      </div>
    )}
  </Section>
);

const ProjectsSection = ({ title, projects }) => (
  <Section section={{ name: title, items: projects, visible: projects.length > 0 }}>
    {proj => (
      <div>
        <div className="flex justify-between items-center">
          <h4 className="text-base font-semibold text-gray-900">{proj.name}</h4>
          {proj.link && <a href={proj.link} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-600 hover:underline">{proj.linkLabel}</a>}
        </div>
        <p className="text-sm text-gray-700">{proj.description}</p>
      </div>
    )}
  </Section>
);

const cardColors = {
  education: "bg-gray-100",
  volunteering: "bg-gray-100",
  publications: "bg-gray-100",
  projects: "bg-gray-100",
  summary: "bg-gray-100",
  experience: "bg-gray-100",
  references: "bg-gray-100",
  skills: "bg-gray-100",
};

const Card = ({ color, children }) => (
  <div className={`${color} rounded-lg shadow p-3 mb-4`}>{children}</div>
);

const Abhi1 = () => {
  const { basics, sections } = sampleResume;
  return (
    <div className="flex justify-center items-start min-h-screen bg-gray-100 py-8">
      <div className="bg-white rounded-lg shadow-lg p-4 my-8 overflow-auto" style={{ width: "210mm", minHeight: "297mm", maxWidth: "100%" }}>
        <HeaderBar basics={basics} />
        <Card color={cardColors.skills}>
          <SkillsBar section={sections.skills} />
        </Card>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Left Column */}
          <div>
            <Card color={cardColors.education}>
              <Section section={sections.education}>{edu => (
                <div>
                  <h4 className="font-bold text-blue-900 mb-1">{edu.institution}</h4>
                  <p className="text-sm font-semibold text-blue-900">{edu.studyType}</p>
                  <p className="text-xs text-gray-600">{edu.area}</p>
                  <p className="text-xs text-gray-600">{edu.date}</p>
                </div>
              )}</Section>
            </Card>
            <Card color={cardColors.volunteering}>
              <Section section={sections.volunteering}>{vol => (
                <div>
                  <h4 className="font-bold text-green-900 mb-1">{vol.organization}</h4>
                  <p className="text-sm font-semibold text-green-900">{vol.position}</p>
                  <p className="text-xs text-gray-600">{vol.date}</p>
                  {vol.summary && <div className="text-sm text-gray-700" dangerouslySetInnerHTML={{ __html: vol.summary }} />}
                </div>
              )}</Section>
            </Card>
            <Card color={cardColors.publications}>
              <PublicationsSection title={sections.publications.name} publications={sections.publications.items} />
            </Card>
            <Card color={cardColors.projects}>
              <ProjectsSection title={sections.projects.name} projects={sections.projects.items} />
            </Card>
          </div>
          {/* Right Column */}
          <div>
            <Card color={cardColors.summary}>
              <Summary section={sections.summary} />
            </Card>
            <Card color={cardColors.experience}>
              <Section section={sections.experience}>{exp => (
                <div>
                  <h4 className="font-bold text-orange-900 mb-1">{exp.company}</h4>
                  <p className="text-sm font-semibold text-orange-900">{exp.position}</p>
                  <p className="text-xs text-gray-600">{exp.date} {exp.location && `| ${exp.location}`}</p>
                  {exp.summary && <div className="text-sm text-gray-700" dangerouslySetInnerHTML={{ __html: exp.summary }} />}
                </div>
              )}</Section>
            </Card>
            <Card color={cardColors.references}>
              <Section section={sections.references}>{ref => (
                <div>
                  <p className="font-semibold text-pink-900">{ref.name}</p>
                  <p className="text-sm text-gray-700">{ref.detail}</p>
                </div>
              )}</Section>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Abhi1;
