import React, { useEffect, useState } from "react";
import { FaBars, FaPlus, FaUserAstronaut, FaTimes } from "react-icons/fa";
import { useResumeStore } from "../../../store/useResumeDetailStore";
import { useAddInfoStore } from "../../../store/addInfoStore";
import { useDeleteInfoStore } from "../../../store/deleteInfoStore";
import { useUpdateInfoStore } from "../../../store/updateInfoStore";
import DataCard from "../../Components/DataCard";
import toast from "react-hot-toast";

const proficiencyLevels = ["Beginner", "Intermediate", "Advanced", "Expert"];

const Skills = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    skill: "",
    proficiency: "Beginner",
    keywords: "",
    description: "",
  });
  const [editingId, setEditingId] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(null);

  const { resume, fetchResume, loading: resumeLoading } = useResumeStore();
  const { addInfo, loading: adding } = useAddInfoStore();
  const { deleteInfo } = useDeleteInfoStore();
  const { updateInfo, loading: updating } = useUpdateInfoStore();

  const resumeId = resume?._id;
  const skills = resume?.Skills || [];

  useEffect(() => {
    if (resumeId) fetchResume(resumeId);
  }, [resumeId]);

  const resetForm = () => {
    setFormData({
      skill: "",
      proficiency: "Beginner",
      keywords: "",
      description: "",
    });
    setEditingId(null);
  };

  const openModal = (skill = null) => {
    if (skill) {
      setFormData({
        skill: skill.Skill,
        proficiency: skill.Proficiency,
        keywords: skill.Keywords?.join(", ") || "",
        description: skill.Description || "",
      });
      setEditingId(skill._id);
    } else {
      resetForm();
    }
    setModalOpen(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!formData.skill.trim())
      return toast.error("Please enter a skill name.");
    if (!resumeId) return toast.error("Resume not loaded yet.");

    const payload = {
      Skill: formData.skill.trim(),
      Proficiency: formData.proficiency,
      Description: formData.description.trim(),
      Keywords: formData.keywords
        .split(",")
        .map((k) => k.trim())
        .filter(Boolean),
    };

    try {
      if (editingId) {
        await updateInfo({
          resumeId,
          section: "skills",
          entryId: editingId,
          updatedData: payload,
        });
        toast.success("Skill updated.");
      } else {
        await addInfo({
          resumeId,
          section: "skills",
          newData: payload,
        });
        toast.success("Skill added.");
      }

      resetForm();
      setModalOpen(false);
      fetchResume(resumeId);
    } catch {
      toast.error("Failed to save skill.");
    }
  };

  const handleDeleteConfirmed = async () => {
    if (!resumeId || !confirmDelete) return;
    try {
      await deleteInfo({
        resumeId,
        section: "skills",
        entryId: confirmDelete._id,
      });
      toast.success("Skill deleted.");
      fetchResume(resumeId);
    } catch {
      toast.error("Delete failed.");
    } finally {
      setConfirmDelete(null);
    }
  };

  return (
    <div className="bg-white min-h-screen py-10 px-4 md:px-8 text-[#29354d] font-sans">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <FaUserAstronaut size={22} className="text-[#29354d]" />
          <h2 className="text-2xl font-extrabold tracking-tight">Skills</h2>
        </div>
        <FaBars size={20} className="text-[#29354d]" />
      </div>

      {/* Add Skill Button */}
      <div
        onClick={() => openModal()}
        className="border-2 border-dashed border-gray-400 rounded-lg py-5 px-8 flex items-center justify-center cursor-pointer bg-gray-200 hover:bg-gray-300 text-[#29354d] font-medium text-base mb-8 transition"
      >
        <FaPlus className="mr-2" />
        Add a new skill
      </div>

      {/* Skills List */}
      <div className="flex flex-wrap gap-4">
        {resumeLoading ? (
          <p className="text-gray-500">Loading skills...</p>
        ) : skills.length === 0 ? (
          <p className="text-gray-500">No skills added yet.</p>
        ) : (
          skills.map((s) => (
            <div key={s._id} className="w-fit min-w-[250px]">
              <DataCard
                title={s.Skill}
                subtitle={s.Proficiency}
                description={
                  <>
                    {s.Description && <p className="mb-1">{s.Description}</p>}
                    {s.Keywords?.length > 0 && (
                      <p className="text-sm text-gray-500">
                        Tags: {s.Keywords.join(", ")}
                      </p>
                    )}
                  </>
                }
                onDelete={() => setConfirmDelete(s)}
                onEdit={() => openModal(s)}
              />
            </div>
          ))
        )}
      </div>

      {/* Skill Modal */}
      {modalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm px-4">
          <div className="relative w-full max-w-md bg-white text-[#29354d] border border-gray-300 rounded-2xl shadow-2xl p-8">
            <button
              onClick={() => {
                resetForm();
                setModalOpen(false);
              }}
              className="absolute top-4 right-4 text-[#29354d] hover:text-black transition"
            >
              <FaTimes size={20} />
            </button>

            <h2 className="text-xl font-bold mb-6 text-center flex items-center justify-center gap-2">
              <FaPlus />
              {editingId ? "Update Skill" : "Add New Skill"}
            </h2>

            <form onSubmit={handleSubmit} className="space-y-4">
              <input
                className="w-full bg-gray-100 border border-gray-300 rounded-lg px-4 py-2 text-sm"
                value={formData.skill}
                onChange={(e) =>
                  setFormData({ ...formData, skill: e.target.value })
                }
                placeholder="e.g. JavaScript"
                required
              />

              <select
                className="w-full bg-gray-100 border border-gray-300 rounded-lg px-4 py-2 text-sm"
                value={formData.proficiency}
                onChange={(e) =>
                  setFormData({ ...formData, proficiency: e.target.value })
                }
              >
                {proficiencyLevels.map((level) => (
                  <option key={level} value={level}>
                    {level}
                  </option>
                ))}
              </select>

              <input
                className="w-full bg-gray-100 border border-gray-300 rounded-lg px-4 py-2 text-sm"
                value={formData.keywords}
                onChange={(e) =>
                  setFormData({ ...formData, keywords: e.target.value })
                }
                placeholder="e.g. React, UI, Frontend"
              />

              <textarea
                className="w-full bg-gray-100 border border-gray-300 rounded-lg px-4 py-2 text-sm"
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                placeholder="Describe this skill (optional)"
              />

              <button
                type="submit"
                disabled={adding || updating}
                className={`w-full ${
                  adding || updating ? "opacity-60 cursor-not-allowed" : ""
                } bg-[#676562] hover:bg-[#000000] text-[#29354d] font-bold px-6 py-2.5 rounded-lg shadow-sm transition`}
              >
                {adding || updating
                  ? "Saving..."
                  : editingId
                  ? "Update Skill"
                  : "Create Skill"}
              </button>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {confirmDelete && (
        <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 px-4">
          <div className="bg-white max-w-md w-full p-6 rounded-lg shadow-xl relative">
            <button
              onClick={() => setConfirmDelete(null)}
              className="absolute top-4 right-4 text-gray-500 hover:text-black"
            >
              <FaTimes size={20} />
            </button>
            <h3 className="text-lg font-bold mb-2 text-[#29354d]">
              Delete Skill
            </h3>
            <p className="text-sm mb-4 text-gray-700">
              Are you sure you want to delete this skill?
            </p>

            <div className="border p-3 rounded bg-gray-50 text-sm mb-4 text-[#29354d]">
              <p>
                <strong>Skill:</strong> {confirmDelete.Skill}
              </p>
              <p>
                <strong>Proficiency:</strong> {confirmDelete.Proficiency}
              </p>
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setConfirmDelete(null)}
                className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-sm"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirmed}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 text-sm"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Skills;
