import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { FaRegStickyNote, FaBars, FaPlus, FaTimes } from "react-icons/fa";
import axios from "axios";
import DataCard from "../../Components/DataCard";
import { useResumeStore } from "../../../store/useResumeDetailStore";
import AddAISummaryModal from "./AddAISummaryModal";
import { API_BASE_URL } from "../../../lib/constants";
// Modal for Add/Update Summary
const AddSummaryModal = ({ open, onClose, onSave, initialValue }) => {

  const [summary, setSummary] = useState(initialValue || "");
  const [aiModalOpen, setAiModalOpen] = useState(false);

  useEffect(() => {
    setSummary(initialValue || "");
  }, [initialValue]);

  if (!open) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black/40 z-50 flex items-center justify-center px-4">
        <div className="relative bg-white text-black p-6 rounded-xl w-full max-w-2xl border border-white/20">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-black text-2xl"
          >
            ×
          </button>

          <h2 className="text-xl font-bold mb-4 flex items-center gap-2">
            <FaPlus className="text-black" />
            {initialValue ? "Update Summary" : "Add Summary"}
          </h2>

          <textarea
            rows={5}
            value={summary}
            onChange={(e) => setSummary(e.target.value)}
            placeholder="Write a brief summary about yourself..."
            className="w-full bg-gray-100 text-black border border-gray-300 rounded px-4 py-2 resize-none"
          />

          <div className="flex justify-between items-center pt-4">
            <button
              onClick={() => setAiModalOpen(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded font-medium hover:bg-blue-700"
            >
              🤖 Generate with AI
            </button>

            <button
              onClick={() => {
                if (summary.trim()) {
                  onSave(summary.trim());
                  setSummary("");
                  onClose();
                }
              }}
              className="bg-black text-white px-6 py-2 rounded font-semibold"
            >
              {initialValue ? "Update" : "Save"}
            </button>
          </div>
        </div>
      </div>

      {/* AI Summary Modal */}
      <AddAISummaryModal
        open={aiModalOpen}
        onClose={() => setAiModalOpen(false)}
        onSave={(generatedSummary) => {
          setSummary(generatedSummary);
          setAiModalOpen(false);
        }}
        initialValue={summary}
      />
    </>
  );
};

const Summary = () => {
  const { resumeId } = useParams();
  const [modalOpen, setModalOpen] = useState(false);
  const [editText, setEditText] = useState("");
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [summaryText, setSummaryText] = useState("");

  const { resume, fetchResume, loading } = useResumeStore();

  useEffect(() => {
    if (resumeId) fetchResume(resumeId, true);
  }, [resumeId]);

  useEffect(() => {
    setSummaryText(resume?.summery || "");
  }, [resume]);

  const handleSave = async (text) => {
    try {
      const payload = { summary: text };
      await axios.post(
        `${API_BASE_URL}/resume/addinfo/addsummery/${resumeId}`,
        payload,
        { withCredentials: true }
      );
      await fetchResume(resumeId, true);
    } catch (err) {
      console.error(" Failed to save summary:", err?.response?.data || err);
      alert(" Failed to save summary.");
    }
  };

  const deleteSummaryHandler = async () => {
    try {
      await axios.delete(
        `${API_BASE_URL}/resume/deleteinfo/${resumeId}/deletesummary`,
        { withCredentials: true }
      );
      await fetchResume(resumeId, true);
    } catch (err) {
      console.error("Failed to delete summary:", err);
      alert(" Failed to delete summary.");
    } finally {
      setConfirmDelete(false);
    }
  };

  return (
    <div className="bg-white min-h-screen py-10 px-4 md:px-8 text-black font-sans">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <FaRegStickyNote className="text-black" />
          <h2 className="text-2xl font-extrabold">Summary</h2>
        </div>
        <FaBars className="text-black" />
      </div>

      {/* Add / Update Button */}
      <div
        onClick={() => {
          setModalOpen(true);
          setEditText(summaryText);
        }}
        className="cursor-pointer border-2 border-dashed border-black rounded-lg py-5 px-8 flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-black text-lg font-medium mb-8"
      >
        <FaPlus className="mr-2" />
        {summaryText ? "Update Summary" : "Add Summary"}
      </div>

      {/* Summary Display */}
      <div className="grid gap-4">
        {loading ? (
          <p className="text-gray-600">Loading...</p>
        ) : !summaryText ? (
          <p className="text-gray-500">No summary added yet.</p>
        ) : (
          <DataCard
            icon={<FaRegStickyNote />}
            title="Summary"
            subtitle={
              summaryText.split(" ").slice(0, 10).join(" ") +
              (summaryText.split(" ").length > 10 ? "..." : "")
            }
            onEdit={() => {
              setEditText(summaryText);
              setModalOpen(true);
            }}
            onDelete={() => setConfirmDelete(true)}
          />
        )}
      </div>

      {/* Add / Update Modal */}
      <AddSummaryModal
        open={modalOpen}
        onClose={() => {
          setModalOpen(false);
          setEditText("");
        }}
        onSave={handleSave}
        initialValue={editText}
      />

      {/* Delete Confirmation Modal */}
      {confirmDelete && (
        <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 px-4">
          <div className="bg-white max-w-md w-full p-6 rounded-lg shadow-xl relative">
            <button
              onClick={() => setConfirmDelete(false)}
              className="absolute top-4 right-4 text-gray-500 hover:text-black"
            >
              <FaTimes size={20} />
            </button>
            <h3 className="text-lg font-bold mb-2 text-black">
              Delete Summary
            </h3>
            <p className="text-sm mb-4 text-gray-700">
              Are you sure you want to delete your summary?
            </p>

            <div className="border p-3 rounded bg-gray-50 text-sm mb-4 text-[#29354d]">
              <p className="line-clamp-3">{summaryText}</p>
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setConfirmDelete(false)}
                className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-sm"
              >
                Cancel
              </button>
              <button
                onClick={deleteSummaryHandler}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 text-sm"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Summary;
