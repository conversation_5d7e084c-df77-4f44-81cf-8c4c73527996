import React from "react";

const Skills = ({
  skills,
  listStyle = "",
  itemStyle = "",
  titleStyle = "",
}) => {
  if (!skills || skills.length === 0) return null;

  return (
    <ul className={`list-disc list-inside ${listStyle}`}>
      {skills.map((skill, idx) => (
        <li key={idx} className={itemStyle}>
          <span className={titleStyle}>{skill}</span>
        </li>
      ))}
    </ul>
  );
};

export default Skills;
