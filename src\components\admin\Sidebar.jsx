import React from "react";
import { NavLink, useNavigate } from "react-router-dom";
import {
    FaUser,
    FaFileAlt,
    FaHome,
    FaCog,
    FaSignOutAlt,
    FaTimes,
} from "react-icons/fa";
import { useAuthStore } from "../../store/authStore"; 

const Sidebar = ({ isOpen, toggle }) => {
    const navigate = useNavigate();
    const logoutHandler = useAuthStore((state) => state.logout); // ✅ get logout method

    const handleLogout = async () => {
        await logoutHandler();            // ✅ call logout from store
        navigate("/login");               // ✅ navigate to login
    };

    const topMenu = [
        { label: "Dashboard", to: "/admin/dashboard", icon: <FaHome /> },
        { label: "Users", to: "/admin/users", icon: <FaUser /> },
        { label: "Resumes", to: "/admin/resumes", icon: <FaFileAlt /> },
        { label: "Settings", to: "/admin/settings", icon: <FaCog /> },
    ];

    return (
        <div
            className={`fixed z-50 top-0 left-0 h-screen w-64 bg-[#29354d] text-white p-4 shadow-lg transition-transform duration-300 transform
      ${isOpen ? "translate-x-0" : "-translate-x-full"} md:translate-x-0 md:relative md:block`}
        >
            <div className="flex flex-col h-full">
                {/* Header */}
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl text-yellow-400 font-bold">Admin</h2>
                    <button className="text-white md:hidden" onClick={toggle}>
                        <FaTimes size={20} />
                    </button>
                </div>

                {/* Main menu */}
                <ul className="space-y-3 flex-1">
                    {topMenu.map((item) => (
                        <li key={item.to}>
                            <NavLink
                                to={item.to}
                                onClick={toggle}
                                className={({ isActive }) =>
                                    `flex items-center gap-3 px-4 py-2 rounded ${isActive
                                        ? "bg-yellow-400 text-black font-semibold"
                                        : "hover:bg-yellow-500 hover:text-black"
                                    }`
                                }
                            >
                                <span className="text-lg">{item.icon}</span>
                                <span>{item.label}</span>
                            </NavLink>
                        </li>
                    ))}
                </ul>

                {/* Logout */}
                <div className="mt-auto pt-4 border-t border-yellow-400">
                    <button
                        onClick={handleLogout}
                        className="w-full flex items-center gap-3 px-4 py-2 rounded hover:bg-yellow-500 hover:text-black"
                    >
                        <span className="text-lg">
                            <FaSignOutAlt />
                        </span>
                        <span>Logout</span>
                    </button>
                </div>
            </div>
        </div>
    );
};

export default Sidebar;
