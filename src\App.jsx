import { useEffect } from "react";
import { Routes, Route } from "react-router-dom";
import { useAuthStore } from "./store/authStore";
import { useResumeStore } from "./store/useResumeDetailStore";

import PrivateRoute from "./components/PrivateRoute";
import Login from "./features/auth/Login";
import RegistrationPage from "./features/auth/Register";
import OTPVerifypage from "./features/auth/OTPVerifypage";
import Dashboard from "./pages/Dashboard";
import Setting from "./pages/Setting";
import Home from "./pages/Home";
import { NotFound } from "./pages/NotFound";
import ResumeEditor from "./pages/ResumeEditor";

import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import AdminRoute from "./routes/AdminRoute";
import AdminLayout from "./admin/AdminLayout";
import AdminUsers from "./admin/AdminUsers";
import AdminResumes from "./admin/AdminResumes";
import AdminDashboard from "./admin/AdminDashboard";
import AdminSettings from "./admin/AdminSettings";

const App = () => {
  const { fetchCurrentUser, user, isHydrated, isLoading } = useAuthStore();
  const { fetchResume, hasFetched } = useResumeStore();

  // Load current user
  useEffect(() => {
    fetchCurrentUser();
  }, []);

  // Once user is known, fetch resume ONCE
  useEffect(() => {
    if (user?.resumeId && !hasFetched) {
      fetchResume(user.resumeId);
    }
  }, [user?.resumeId, hasFetched, fetchResume]);

  if (isLoading && !isHydrated) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <p className="text-gray-600">Loading…</p>
      </div>
    );
  }

  return (
    <>
      {/* Toast Container */}
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />

      {/* App Routes */}
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/login" element={<Login />} />

        <Route path="/register" element={<RegistrationPage />} />
        <Route path="/verify-otp" element={<OTPVerifypage />} />
        {/* <Route path="*" element={<NotFound />} /> */}
        <Route
          path="/dashboard"
          element={
            <PrivateRoute>
              <Dashboard />
            </PrivateRoute>
          }
        />
        <Route
          path="/dashboard/settings"
          element={
            <PrivateRoute>
              <Setting />
            </PrivateRoute>
          }
        />
        <Route
          path="/resume/new"
          element={
            <PrivateRoute>
              <ResumeEditor />
            </PrivateRoute>
          }
        />
        <Route
          path="/resume/:resumeId"
          element={
            <PrivateRoute>
              <ResumeEditor />
            </PrivateRoute>
          }
        />
        <Route
          path="/admin"
          element={
            <AdminRoute>
              <AdminLayout />
            </AdminRoute>
          }
        >
          <Route path="dashboard" element={<AdminDashboard />} />
          <Route path="users" element={<AdminUsers />} />
          <Route path="resumes" element={<AdminResumes />} />
          <Route path="settings" element={<AdminSettings />} />
        </Route>
      </Routes>
    </>
  );
};

export default App;
