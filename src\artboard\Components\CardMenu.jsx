import React from "react";

export function CardMenu({ visibleChecked = false }) {
  return (
    <div className="w-44 bg-white rounded-lg shadow-2xl py-2 text-black font-sans text-base">
      <div className="flex items-center gap-3 px-5 py-2 cursor-pointer hover:bg-[#1b1b1e] rounded-t-lg">
        <span className="w-5 h-5 flex items-center justify-center">
          {visibleChecked ? (
            <svg width="18" height="18" viewBox="0 0 20 20" fill="none">
              <path
                d="M5 10.5l3.5 3.5 6-7"
                stroke="#fff"
                strokeWidth="2"
                fill="none"
              />
            </svg>
          ) : null}
        </span>
        Visible
      </div>
      <div className="flex items-center gap-3 px-5 py-2 cursor-pointer hover:bg-[#1b1b1e] border-t border-[#232327]">
        <span className="w-5 h-5 flex items-center justify-center">
          <svg width="18" height="18" viewBox="0 0 20 20" fill="none">
            <path
              d="M3 17v-3.5l9.06-9.06a1.5 1.5 0 012.12 0l1.38 1.38a1.5 1.5 0 010 2.12L6.5 17H3z"
              stroke="#fff"
              strokeWidth="1.5"
              fill="none"
            />
          </svg>
        </span>
        Edit
      </div>
      <div className="flex items-center gap-3 px-5 py-2 cursor-pointer hover:bg-[#1b1b1e] border-t border-[#232327]">
        <span className="w-5 h-5 flex items-center justify-center">
          <svg width="18" height="18" viewBox="0 0 20 20" fill="none">
            <rect
              x="6"
              y="6"
              width="8"
              height="8"
              stroke="#fff"
              strokeWidth="1.5"
              fill="none"
            />
            <rect
              x="3"
              y="3"
              width="8"
              height="8"
              stroke="#fff"
              strokeWidth="1.5"
              fill="none"
            />
          </svg>
        </span>
        Copy
      </div>
      <div className="flex items-center gap-3 px-5 py-2 cursor-pointer hover:bg-[#1b1b1e] border-t border-[#232327] text-[#f05365]">
        <span className="w-5 h-5 flex items-center justify-center">
          <svg width="18" height="18" viewBox="0 0 20 20" fill="none">
            <rect
              x="5"
              y="5"
              width="10"
              height="10"
              stroke="#f05365"
              strokeWidth="1.5"
              fill="none"
            />
            <line
              x1="7"
              y1="7"
              x2="13"
              y2="13"
              stroke="#f05365"
              strokeWidth="1.5"
            />
            <line
              x1="13"
              y1="7"
              x2="7"
              y2="13"
              stroke="#f05365"
              strokeWidth="1.5"
            />
          </svg>
        </span>
        Remove
      </div>
    </div>
  );
}
