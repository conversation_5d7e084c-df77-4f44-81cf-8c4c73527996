import React, { useState, useEffect } from "react";
import { FaBars, FaLanguage, FaPlus, FaTimes } from "react-icons/fa";
import { useResumeStore } from "../../../store/useResumeDetailStore";
import { useAddInfoStore } from "../../../store/addInfoStore";
import { useDeleteInfoStore } from "../../../store/deleteInfoStore";
import { useUpdateInfoStore } from "../../../store/updateInfoStore";
import DataCard from "../../Components/DataCard";
import toast from "react-hot-toast";

const proficiencyOptions = ["Basic", "Conversational", "Fluent", "Native"];

const Languages = () => {
  const { resume, fetchResume } = useResumeStore();
  const { addInfo, loading: adding } = useAddInfoStore();
  const { deleteInfo } = useDeleteInfoStore();
  const { updateInfo, loading: updating } = useUpdateInfoStore();

  const resumeId = resume?._id;
  const languages = resume?.Languages || [];

  const [showModal, setShowModal] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(null);

  const [formData, setFormData] = useState({
    name: "",
    proficiency: "Basic",
    description: "",
  });

  useEffect(() => {
    if (resumeId) fetchResume(resumeId);
  }, [resumeId]);

  const resetForm = () => {
    setFormData({ name: "", proficiency: "Basic", description: "" });
    setEditingId(null);
  };

  const openModal = (lang = null) => {
    if (lang) {
      setFormData({
        name: lang.Name,
        proficiency: lang.Proficiency,
        description: lang.Description || "",
      });
      setEditingId(lang._id);
    } else {
      resetForm();
    }
    setShowModal(true);
  };

  const closeModal = () => {
    resetForm();
    setShowModal(false);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCreateOrUpdate = async () => {
    const { name, proficiency, description } = formData;

    if (!name || !proficiency) {
      toast.error("Language name and proficiency are required.");
      return;
    }

    const payload = {
      Name: name,
      Proficiency: proficiency,
      Description: description,
    };

    try {
      if (editingId) {
        await updateInfo({
          resumeId,
          section: "languages",
          entryId: editingId,
          updatedData: payload,
        });
        toast.success("Language updated successfully!");
      } else {
        await addInfo({
          resumeId,
          section: "languages",
          newData: payload,
        });
        toast.success("Language added successfully!");
      }

      await fetchResume(resumeId);
      closeModal();
    } catch (err) {
      toast.error("Something went wrong.");
    }
  };

  const confirmDeleteLanguage = async () => {
    if (!resumeId || !confirmDelete) return;

    try {
      await deleteInfo({
        resumeId,
        section: "languages",
        entryId: confirmDelete._id,
      });
      await fetchResume(resumeId);
      toast.success("Language deleted.");
    } catch (err) {
      toast.error("Failed to delete language.");
    } finally {
      setConfirmDelete(null);
    }
  };

  return (
    <div className="bg-white min-h-screen py-10 px-4 md:px-8 text-[#29354d] font-sans">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <FaLanguage className="text-[#29354d]" size={22} />
          <h2 className="text-2xl font-extrabold">Languages</h2>
        </div>
        <FaBars size={20} className="text-[#29354d]" />
      </div>

      {/* Add Button */}
      <div
        onClick={() => openModal()}
        className="cursor-pointer border-2 border-dashed border-gray-400 rounded-lg py-5 px-8 flex items-center justify-center bg-gray-200 hover:bg-gray-300 text-[#29354d] text-lg font-medium mb-8"
      >
        <FaPlus className="mr-2" />
        Add a new language
      </div>

      {/* Language List */}
      <div className="flex flex-wrap gap-4">
        {languages.length === 0 ? (
          <p className="text-gray-500">No languages added yet.</p>
        ) : (
          languages.map((lang) => (
            <div key={lang._id} className="w-fit min-w-[250px]">
              <DataCard
                title={lang.Name}
                subtitle={lang.Proficiency}
                description={lang.Description || "No description provided."}
                onEdit={() => openModal(lang)}
                onDelete={() => setConfirmDelete(lang)}
              />
            </div>
          ))
        )}
      </div>

      {/* Create / Edit Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center px-4">
          <div className="relative w-full max-w-xl bg-white rounded-2xl p-6 shadow-xl">
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 text-[#29354d] hover:text-black transition"
            >
              <FaTimes size={20} />
            </button>

            <h2 className="text-xl font-bold mb-6 text-[#29354d] flex items-center gap-2">
              <FaPlus /> {editingId ? "Update Language" : "Add New Language"}
            </h2>

            <form onSubmit={(e) => e.preventDefault()} className="space-y-4">
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Language Name"
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              />
              <select
                name="proficiency"
                value={formData.proficiency}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              >
                <option disabled value="">
                  Select Proficiency
                </option>
                {proficiencyOptions.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Describe your language usage..."
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm resize-vertical min-h-[80px]"
              />
              <div className="flex justify-end">
                <button
                  onClick={handleCreateOrUpdate}
                  disabled={adding || updating}
                  className={`${
                    adding || updating ? "opacity-60 cursor-not-allowed" : ""
                  } bg-[#949089] hover:bg-[#171616] text-[#e6dada] font-semibold px-6 py-2 rounded-md transition`}
                >
                  {adding || updating
                    ? "Saving..."
                    : editingId
                    ? "Update"
                    : "Create"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {confirmDelete && (
        <div className="fixed inset-0 z-50 bg-black/60 flex items-center justify-center px-4">
          <div className="bg-white max-w-md w-full p-6 rounded-lg shadow-xl text-[#29354d]">
            <button
              onClick={() => setConfirmDelete(null)}
              className="absolute top-4 right-4 text-gray-500 hover:text-black"
            >
              <FaTimes size={20} />
            </button>
            <h3 className="text-lg font-bold mb-2">Delete Language</h3>
            <p className="text-sm mb-4">
              Are you sure you want to delete{" "}
              <strong>{confirmDelete.Name}</strong>?
            </p>

            <div className="border p-3 rounded bg-gray-50 text-sm mb-4">
              <p>
                <strong>Proficiency:</strong> {confirmDelete.Proficiency}
              </p>
              {confirmDelete.Description && (
                <p className="mt-2">
                  <strong>Description:</strong> {confirmDelete.Description}
                </p>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setConfirmDelete(null)}
                className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-sm"
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteLanguage}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 text-sm"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Languages;
