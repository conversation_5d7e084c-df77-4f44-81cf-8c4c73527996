// import statements remain unchanged
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../store/authStore";
import { useResumeStore } from "../store/resumeStore";
import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import tumb1 from "../../public/templets/2.avif";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import CreateResumeModal from "../components/CreateResumeModal";

import {
  FiPlus,
  FiGrid,
  FiList,
  FiLogIn,
  FiSettings,
  FiEdit,
  FiFile,
  FiTrash2,
  FiCopy,
  FiGlobe,
  FiUpload,
  FiDownload,
} from "react-icons/fi";
import { FaRegFileAlt, FaRegFileWord } from "react-icons/fa";

const SIDEBAR_COLLAPSED_WIDTH = 72;
const SIDEBAR_EXPANDED_WIDTH = 256;

const Dashboard = () => {
  const navigate = useNavigate();
  const {
    user,
    isLoading: authLoading,
    fetchCurrentUser,
    logout,
  } = useAuthStore();

  const {
    resumes,
    fetchAllResumes,
    deleteResume,
    updateResumeTitle,
    duplicateResume,
    togglePublication,
    getPublishURL,
  } = useResumeStore();

  const [authChecked, setAuthChecked] = useState(false);
  const [viewMode, setViewMode] = useState("grid");
  const [currentPage, setCurrentPage] = useState(1);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(null);
  const [editingTitleId, setEditingTitleId] = useState(null);
  const [editingTitleVal, setEditingTitleVal] = useState("");
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const resumesPerPage = 8;
  const indexOfLastResume = currentPage * resumesPerPage;
  const currentResumes = resumes.slice(
    indexOfLastResume - resumesPerPage,
    indexOfLastResume
  );
  const totalPages = Math.ceil(resumes.length / resumesPerPage);

  useEffect(() => {
    (async () => {
      try {
        await fetchCurrentUser();
        await fetchAllResumes();
      } finally {
        setAuthChecked(true);
      }
    })();
  }, []);

  useEffect(() => {
    if (authChecked && !authLoading && !user) {
      navigate("/login", { replace: true });
    }
  }, [user, authLoading, authChecked]);

  const handleDelete = async (id) => {
    try {
      await deleteResume(id);
      toast.success("Deleted successfully");
      setShowDeleteModal(null);
    } catch {
      toast.error("Unable to delete");
    }
  };

  const handleTitleSave = async (id) => {
    try {
      await updateResumeTitle(id, editingTitleVal);
      toast.success("Title updated");
      setEditingTitleId(null);
      await fetchAllResumes();
    } catch {
      toast.error("Update failed");
    }
  };

  const handleResumeCreated = async (resumeId) => {
    await fetchAllResumes();
    navigate(`/resume/${resumeId}`);
  };

  const handleDuplicate = async (id) => {
    try {
      const newResume = await duplicateResume(id);
      if (newResume?._id) {
        toast.success("Resume duplicated");
      }
    } catch {
      toast.error("Failed to duplicate resume");
    }
  };

  const handleCopyLink = async (id) => {
    try {
      const url = await getPublishURL(id);
      navigator.clipboard.writeText(url);
      toast.success("Link copied!");
    } catch {
      toast.error("Failed to copy link");
    }
  };

  const handleTogglePublish = async (id, status) => {
    try {
      await togglePublication(id, !status);
    } catch {
      toast.error("Failed to toggle publish");
    }
  };

  if (!authChecked || authLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <DotLottieReact
          src="https://lottie.host/85cf8f03-77a5-4adc-a588-b359fdd21a94/jhr4dHrvp2.lottie"
          loop
          autoplay
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex bg-[#29354d]">
      {/* Sidebar */}
      <aside
        className="h-screen bg-[#29354d] flex flex-col justify-between fixed z-20 transition-all duration-300"
        onMouseEnter={() => setSidebarOpen(true)}
        onMouseLeave={() => setSidebarOpen(false)}
        style={{
          width: sidebarOpen ? SIDEBAR_EXPANDED_WIDTH : SIDEBAR_COLLAPSED_WIDTH,
        }}
      >
        <div className="p-4 flex items-center justify-center h-20">
          {sidebarOpen ? (
            <h1 className="text-2xl font-bold text-[#fcc250]">ResumeBuilder</h1>
          ) : (
            <div className="rounded-full w-12 h-12 flex items-center justify-center bg-[#fcc250] text-[#29354d] font-bold text-xl">
              R
            </div>
          )}
        </div>
        <nav className="flex-1">
          {[
            // buttons
            {
              icon: <FiPlus />,
              onClick: () => setShowCreateModal(true),
              label: "New",
            },
            {
              icon: <FiFile />,
              onClick: () => navigate("/dashboard"),
              label: "Dashboard",
            },
            {
              icon: <FaRegFileAlt />,
              onClick: () => navigate("/resumes"),
              label: "Resumes",
            },
            {
              icon: <FaRegFileWord />,
              onClick: () => navigate("/cover-letters"),
              label: "Cover Letters",
            },
            {
              icon: <FiSettings />,
              onClick: () => navigate("/dashboard/settings"),
              label: "Settings",
            },
          ].map((item, i) => (
            <button
              key={i}
              onClick={item.onClick}
              className={`flex items-center w-full px-4 py-3 rounded-lg text-[#fcc250] hover:bg-[#fcc250] hover:text-[#29354d] ${
                sidebarOpen ? "justify-start space-x-3" : "justify-center"
              }`}
            >
              {item.icon}
              {sidebarOpen && <span>{item.label}</span>}
            </button>
          ))}
        </nav>
        <button
          onClick={async () => (await logout()) && navigate("/login")}
          className="p-4 text-[#fcc250] hover:bg-[#fcc250] hover:text-[#29354d]"
        >
          <FiLogIn />
          {sidebarOpen && <span className="ml-2">Logout</span>}
        </button>
      </aside>

      {/* Main Section */}
      <main
        className="flex-1 transition-all duration-300"
        style={{
          marginLeft: sidebarOpen
            ? SIDEBAR_EXPANDED_WIDTH
            : SIDEBAR_COLLAPSED_WIDTH,
        }}
      >
        <div className="p-8 bg-gray-50 min-h-screen">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-2xl font-bold text-gray-800">Resumes</h1>
            <div className="flex space-x-2 bg-white p-1 rounded-lg shadow-sm">
              <FiGrid
                className={`p-2 rounded-md ${
                  viewMode === "grid" ? "bg-gray-200" : "hover:bg-gray-100"
                }`}
                onClick={() => setViewMode("grid")}
              />
              <FiList
                className={`p-2 rounded-md ${
                  viewMode === "list" ? "bg-gray-200" : "hover:bg-gray-100"
                }`}
                onClick={() => setViewMode("list")}
              />
            </div>
          </div>

          <div
            className={`grid gap-6 ${
              viewMode === "grid" ? "lg:grid-cols-4" : "grid-cols-1"
            }`}
          >
            <div
              className="bg-white rounded-xl border-2 border-dashed border-gray-300 p-8 flex flex-col items-center justify-center h-64 cursor-pointer hover:border-[#29354d]"
              onClick={() => setShowCreateModal(true)}
            >
              <FiPlus className="text-[#29354d] text-2xl mb-4" />
              <h3 className="text-gray-700 font-medium">Create new resume</h3>
            </div>

            {currentResumes.length === 0 ? (
              <div className="col-span-full text-gray-400 text-center mt-12">
                <span className="text-5xl">📝</span>
                <p>No resumes yet.</p>
              </div>
            ) : (
              currentResumes.map((resume) => (
                <div
                  key={resume._id}
                  className="bg-white rounded-xl shadow-sm hover:shadow-md border relative cursor-pointer"
                  onClick={() => navigate(`/resume/${resume._id}`)}
                >
                  <img
                    src={tumb1}
                    alt="thumb"
                    className="h-40 w-full object-cover"
                  />
                  <div className="p-4 space-y-2">
                    {editingTitleId === resume._id ? (
                      <input
                        value={editingTitleVal}
                        onChange={(e) => setEditingTitleVal(e.target.value)}
                        onBlur={() => handleTitleSave(resume._id)}
                        className="border px-2 py-1 w-full"
                        autoFocus
                      />
                    ) : (
                      <h3
                        onDoubleClick={() => {
                          setEditingTitleId(resume._id);
                          setEditingTitleVal(resume.Title);
                        }}
                        className="font-medium text-gray-900"
                      >
                        {resume.Title}
                      </h3>
                    )}
                    <p className="text-sm text-gray-500">
                      {new Date(resume.updatedAt).toLocaleDateString()}
                    </p>

                    <div className="flex flex-wrap gap-2 mt-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDuplicate(resume._id);
                        }}
                        title="Duplicate"
                      >
                        <FiCopy />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTogglePublish(resume._id, resume.isPublished);
                        }}
                        title="Toggle Publish"
                      >
                        {resume.isPublished ? (
                          <FiUpload className="text-green-600" />
                        ) : (
                          <FiDownload className="text-gray-400" />
                        )}
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCopyLink(resume._id);
                        }}
                        title="Copy Link"
                      >
                        <FiGlobe />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowDeleteModal(resume._id);
                        }}
                        title="Delete"
                      >
                        <FiTrash2 className="text-red-500" />
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Pagination */}
          <div className="flex justify-center mt-4 space-x-2">
            {Array.from({ length: totalPages }).map((_, i) => (
              <button
                key={i}
                onClick={() => setCurrentPage(i + 1)}
                className={`px-3 py-1 rounded ${
                  currentPage === i + 1
                    ? "bg-[#29354d] text-[#fcc250]"
                    : "bg-gray-100 hover:bg-gray-200"
                }`}
              >
                {i + 1}
              </button>
            ))}
          </div>
        </div>
      </main>

      {/* Modals */}
      {showCreateModal && (
        <CreateResumeModal
          onClose={() => setShowCreateModal(false)}
          onSuccess={handleResumeCreated}
        />
      )}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-lg font-semibold">Confirm deletion?</h2>
            <div className="mt-4 flex justify-end space-x-3">
              <button
                className="px-4 py-2"
                onClick={() => setShowDeleteModal(null)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-red-600 text-white rounded"
                onClick={() => handleDelete(showDeleteModal)}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      <ToastContainer position="top-right" autoClose={3000} />
    </div>
  );
};

export default Dashboard;
