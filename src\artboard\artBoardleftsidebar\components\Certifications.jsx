import React, { useEffect, useState } from "react";
import { FaCertificate, FaBars, FaPlus, FaTimes } from "react-icons/fa";
import { useResumeStore } from "../../../store/useResumeDetailStore";
import { useAddInfoStore } from "../../../store/addInfoStore";
import { useDeleteInfoStore } from "../../../store/deleteInfoStore";
import { useUpdateInfoStore } from "../../../store/updateInfoStore";
import DataCard from "../../Components/DataCard";
import { format } from "date-fns";
import toast from "react-hot-toast";

const Certifications = () => {
  const { resume, fetchResume } = useResumeStore();
  const resumeId = resume?._id;
  const certifications = resume?.Certifications || [];

  const { addInfo, loading: adding } = useAddInfoStore();
  const { deleteInfo } = useDeleteInfoStore();
  const { updateInfo, loading: updating } = useUpdateInfoStore();

  const [showModal, setShowModal] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(null);

  const [formData, setFormData] = useState({
    title: "",
    organization: "",
    date: "",
    url: "",
    description: "",
  });

  useEffect(() => {
    if (resumeId) fetchResume(resumeId);
  }, [resumeId]);

  const resetForm = () => {
    setFormData({
      title: "",
      organization: "",
      date: "",
      url: "",
      description: "",
    });
    setEditingId(null);
  };

  const closeModal = () => {
    resetForm();
    setShowModal(false);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCreateOrUpdate = async () => {
    const { title, organization, date, url, description } = formData;
    if (!title || !organization || !date) {
      toast.error("Please fill in all required fields.");
      return;
    }

    const payload = {
      Title: title,
      Issuer: organization,
      Date: date,
      Website: url,
      Description: description,
    };

    try {
      if (editingId) {
        await updateInfo({
          resumeId,
          section: "certifications",
          entryId: editingId,
          updatedData: payload,
        });
        toast.success("Certification updated!");
      } else {
        await addInfo({
          resumeId,
          section: "certifications",
          newData: payload,
        });
        toast.success("Certification added!");
      }

      await fetchResume(resumeId);
      closeModal();
    } catch (err) {
      toast.error("Failed to save certification.");
    }
  };

  const confirmDeleteCertification = async () => {
    if (!resumeId || !confirmDelete) return;

    try {
      await deleteInfo({
        resumeId,
        section: "certifications",
        entryId: confirmDelete._id,
      });
      await fetchResume(resumeId);
      toast.success("Certification deleted.");
    } catch (err) {
      toast.error("Delete failed.");
    } finally {
      setConfirmDelete(null);
    }
  };

  const handleEdit = (cert) => {
    setFormData({
      title: cert.Title,
      organization: cert.Issuer,
      date: cert.Date?.split("T")[0] || "",
      url: cert.Website,
      description: cert.Description,
    });
    setEditingId(cert._id);
    setShowModal(true);
  };

  return (
    <div className="bg-white min-h-screen py-10 px-4 md:px-8 text-[#29354d] font-sans">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <FaCertificate className="text-[#29354d]" size={22} />
          <h2 className="text-2xl font-extrabold">Certifications</h2>
        </div>
        <FaBars size={20} className="text-[#29354d]" />
      </div>

      <div
        onClick={() => {
          resetForm();
          setShowModal(true);
        }}
        className="cursor-pointer border-2 border-dashed border-gray-400 rounded-lg py-5 px-8 flex items-center justify-center bg-gray-200 hover:bg-gray-300 text-[#29354d] text-lg font-medium mb-8"
      >
        <FaPlus className="mr-2" />
        Add a new certification
      </div>

      {certifications.length === 0 ? (
        <p className="text-gray-500">No certifications added yet.</p>
      ) : (
        <div className="space-y-4">
          {certifications.map((cert) => (
            <DataCard
              key={cert._id}
              title={cert.Title}
              subtitle={`${cert.Issuer} • ${cert.Date ? format(new Date(cert.Date), "MMM yyyy") : ""
                }`}
              description={
                <>
                  {cert.Description && (
                    <p className="mb-1">{cert.Description}</p>
                  )}
                  {cert.Website && (
                    <a
                      href={cert.Website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 underline"
                    >
                      {cert.Website}
                    </a>
                  )}
                </>
              }
              onDelete={() => setConfirmDelete(cert)}
              onEdit={() => handleEdit(cert)}
            />
          ))}
        </div>
      )}

      {showModal && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center px-4">
          <div className="relative w-full max-w-xl bg-white rounded-2xl p-6 shadow-xl">
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 text-[#29354d] hover:text-black transition"
            >
              <FaTimes size={20} />
            </button>
            <h2 className="text-xl font-bold mb-6 text-[#29354d] flex items-center gap-2">
              <FaPlus />
              {editingId ? "Update Certification" : "Add New Certification"}
            </h2>

            <form onSubmit={(e) => e.preventDefault()} className="space-y-4">
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Certification Title"
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-[#29354d]"
              />
              <input
                type="text"
                name="organization"
                value={formData.organization}
                onChange={handleInputChange}
                placeholder="Issuing Organization"
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-[#29354d]"
              />
              <input
                type="date"
                name="date"
                value={formData.date}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-[#29354d]"
              />
              <input
                type="url"
                name="url"
                value={formData.url}
                onChange={handleInputChange}
                placeholder="Credential URL"
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-[#29354d]"
              />
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Description (optional)"
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-[#29354d] resize-vertical min-h-[80px]"
              />
              <div className="flex justify-end">
                <button
                  type="submit"
                  onClick={handleCreateOrUpdate}
                  disabled={adding || updating}
                  className={`${adding || updating ? "opacity-60 cursor-not-allowed" : ""
                    } bg-[#73716c] hover:bg-[#000000] text-[#f9f9f9] font-semibold px-6 py-2 rounded-md transition`}
                >
                  {adding || updating
                    ? "Saving..."
                    : editingId
                      ? "Update"
                      : "Create"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation */}
      {confirmDelete && (
        <div className="fixed inset-0 z-50 bg-black/60 flex items-center justify-center px-4">
          <div className="bg-white max-w-md w-full p-6 rounded-lg shadow-xl text-[#29354d]">
            <h3 className="text-lg font-bold mb-2">Delete Certification</h3>
            <p className="text-sm mb-4">
              Are you sure you want to delete{" "}
              <strong>{confirmDelete.Title}</strong>?
            </p>

            <div className="border p-3 rounded bg-gray-50 text-sm mb-4">
              <p>
                <strong>Issuer:</strong> {confirmDelete.Issuer}
              </p>
              <p>
                <strong>Date:</strong>{" "}
                {new Date(confirmDelete.Date).toLocaleDateString()}
              </p>
              {confirmDelete.Website && (
                <p>
                  <strong>URL:</strong>{" "}
                  <a
                    href={confirmDelete.Website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 underline"
                  >
                    {confirmDelete.Website}
                  </a>
                </p>
              )}
              {confirmDelete.Description && (
                <p className="mt-2">
                  <strong>Description:</strong> {confirmDelete.Description}
                </p>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setConfirmDelete(null)}
                className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-sm"
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteCertification}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 text-sm"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Certifications;
