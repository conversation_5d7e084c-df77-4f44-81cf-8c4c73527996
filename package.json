{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hello-pangea/dnd": "^18.0.1", "@heroicons/react": "^2.2.0", "@lottiefiles/dotlottie-react": "^0.14.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^5.81.2", "@trpc/client": "^11.4.2", "@trpc/react-query": "^11.4.2", "antd": "^5.26.2", "axios": "^1.10.0", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "flowbite-react": "^0.11.8", "framer-motion": "^12.19.1", "html-docx-js-typescript": "^0.1.5", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jszip": "^3.10.1", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-datepicker": "^8.4.0", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-modal": "^3.16.3", "react-router-dom": "^7.6.2", "react-toastify": "^11.0.5", "xlsx": "^0.18.5", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "vite": "^6.3.5"}}