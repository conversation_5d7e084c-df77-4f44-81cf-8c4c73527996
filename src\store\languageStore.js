import { create } from "zustand";
import axios from "axios";
import {
    ADDINFO_ENDPOINTS,
    GETINFO_ENDPOINT,
    DELETE_INFO,
} from "../lib/constants";

export const useLanguageStore = create((set, get) => ({
    languages: [],
    isLoading: false,
    error: null,

    fetchLanguages: async () => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.get(GETINFO_ENDPOINT, {
                withCredentials: true,
            });
            set({
                languages: res.data.user?.Languages || [],
                isLoading: false,
            });
        } catch (err) {
            set({
                error: err.response?.data?.error || "Failed to load languages",
                isLoading: false,
            });
        }
    },

    addLanguage: async (data) => {
        try {
            await axios.post(ADDINFO_ENDPOINTS.LANGUAGES, data, {
                withCredentials: true,
            });
            // Refresh the language list after adding
            await get().fetchLanguages();
        } catch (err) {
            console.error("Error adding language:", err.response?.data || err.message);
        }
    },

    deleteLanguage: async (language) => {
        try {
            await axios.delete(DELETE_INFO.LANGUAGES(language._id), {
                withCredentials: true,
            });
            // Refresh the language list after deletion
            await get().fetchLanguages();
        } catch (error) {
            console.error("Failed to delete language:", error);
        }
    },

    updateLanguage: async (id, data) => {
        try {
            await axios.put(`${ADDINFO_ENDPOINTS.LANGUAGES}/${id}`, data, {
                withCredentials: true,
            });
            // Refresh the language list after update
            await get().fetchLanguages();
        } catch (err) {
            console.error("Error updating language:", err.response?.data || err.message);
        }
    },
}));
