import React, { useState, useEffect } from "react";
import { FaPlus } from "react-icons/fa";

const AddAISummaryModal = ({ open, onClose, onSave, initialValue }) => {
    const [summary, setSummary] = useState(initialValue || "");
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState({
        name: "",
        position: "",
        experience: "",
        education: "",
        skills: "",
        languages: "",
        certifications: "",
        projects: "",
    });

    useEffect(() => {
        setSummary(initialValue || "");
    }, [initialValue]);
    const generateSummary = async () => {
        setLoading(true);
        const prompt = `Please write a 2–3 line professional resume summary in the first-person (start with "I'm a …"), using details:
      - Name: ${formData.name}
      - Role: ${formData.position}
      …`; // continue all fields

        try {
            const res = await fetch(
                // "https://api-inference.huggingface.co/models/bigscience/bloomz-560m",
                {
                method: "POST",
                headers: {
                    // Authorization: `Bearer *************************************`,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ inputs: prompt }),
            });
            if (!res.ok) throw new Error(await res.text());
            const data = await res.json();
            const text = Array.isArray(data) ? data[0]?.generated_text : data.generated_text;
            setSummary(text?.trim() || "❌ Could not generate summary.");
        } catch (err) {
            console.error("HF error:", err);
            alert("❌ Failed to generate summary. Please check your token and model.");
        } finally {
            setLoading(false);
        }
    };
      
    if (!open) return null;

    return (
        <div className="fixed inset-0 bg-black/40 z-50 flex items-center justify-center px-4">
            <div className="relative bg-white text-black p-6 rounded-xl w-full max-w-2xl border border-white/20 max-h-[90vh] overflow-y-auto">
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-black text-2xl"
                >
                    ×
                </button>

                <h2 className="text-xl font-bold mb-4 flex items-center gap-2">
                    <FaPlus className="text-black" />
                    {initialValue ? "Update Summary" : "Add Summary"}
                </h2>
                <div className="grid gap-3 mb-4">
                    {[
                        "name",
                        "position",
                        "experience",
                        "education",
                        "skills",
                        "languages",
                        "certifications",
                        "projects",
                    ].map((field) => (
                        <input
                            key={field}
                            type="text"
                            placeholder={field.charAt(0).toUpperCase() + field.slice(1)}
                            value={formData[field]}
                            onChange={(e) =>
                                setFormData({ ...formData, [field]: e.target.value })
                            }
                            className="w-full bg-gray-100 border border-gray-300 rounded px-4 py-2"
                        />
                    ))}
                </div>

                <button
                    onClick={generateSummary}
                    disabled={loading}
                    className="bg-blue-600 text-white px-5 py-2 rounded mb-4 disabled:opacity-50"
                >
                    {loading ? "Generating..." : "Generate with AI"}
                </button>

                <textarea
                    rows={5}
                    value={summary}
                    onChange={(e) => setSummary(e.target.value)}
                    placeholder="AI-generated summary will appear here..."
                    className="w-full bg-gray-100 text-black border border-gray-300 rounded px-4 py-2 resize-none"
                />

                <div className="flex justify-end pt-4">
                    <button
                        onClick={() => {
                            if (summary.trim()) {
                                onSave(summary.trim());
                                setSummary("");
                                onClose();
                            }
                        }}
                        className="bg-black text-white px-6 py-2 rounded font-semibold"
                    >
                        {initialValue ? "Update" : "Save"}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default AddAISummaryModal;
