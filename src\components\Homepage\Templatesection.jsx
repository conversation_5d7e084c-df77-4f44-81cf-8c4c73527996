import React, { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";

const templates = [
  "2025.avif",
  "advanced.avif",
  "clean.avif",
  "corporate.avif",
  "elegant.avif",
  "majestic.avif",
  "Minimalist.webp",
  "modern.avif",
  "Templet1.webp",
];

export default function Templatesection() {
  const images = [...templates, ...templates];
  const [isPaused, setIsPaused] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [direction, setDirection] = useState(-1);
  const scrollRef = useRef(null);

  // Drag-To-Scroll Logic
  const isDragging = useRef(false);
  const startX = useRef(0);
  const scrollLeft = useRef(0);

  const handleMouseDown = (e) => {
    isDragging.current = true;
    startX.current = e.pageX - scrollRef.current.offsetLeft;
    scrollLeft.current = scrollRef.current.scrollLeft;
  };
  const handleMouseLeave = () => {
    isDragging.current = false;
  };
  const handleMouseUp = () => {
    isDragging.current = false;
  };
  const handleMouseMove = (e) => {
    if (!isDragging.current) return;
    e.preventDefault();
    const x = e.pageX - scrollRef.current.offsetLeft;
    const walk = (x - startX.current) * 1;
    scrollRef.current.scrollLeft = scrollLeft.current - walk;
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setDirection((prev) => -prev);
    }, 15000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (!isPaused) {
      const interval = setInterval(() => {
        setActiveIndex((prev) => (prev + 1) % templates.length);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isPaused]);

  return (
    <section className="bg-gradient-to-r from-[#f5f7fb] via-[#f5f7fb] to-white rounded-br-[56px] py-16 px-6">
      <div className="max-w-5xl mx-auto text-center space-y-3">
        <h2 className="text-3xl font-bold text-[#29354d]">
          Job‑Winning Resume Templates
        </h2>
        <p className="text-lg text-gray-600">
          Our templates are designed and approved by HR experts to fit a wide
          range of jobs and experience levels. Choose your favorite to showcase
          your professional background and make your resume stand out.
        </p>
      </div>

      <div
        ref={scrollRef}
        className="relative mt-10 overflow-x-scroll no-scrollbar cursor-grab"
        onMouseEnter={() => setIsPaused(true)}
        onMouseLeave={handleMouseLeave}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseMove={handleMouseMove}
      >
        <motion.div
          className="flex space-x-6"
          animate={{
            x: isPaused ? 0 : direction > 0 ? ["0%", "25%"] : ["0%", "-25%"],
          }}
          transition={{
            repeat: isPaused ? 0 : Infinity,
            repeatType: "mirror",
            duration: isPaused ? 0 : 25,
            ease: "linear",
          }}
        >
          {images.map((img, idx) => {
            const isActive =
              activeIndex % templates.length === idx % templates.length;

            return (
              <div
                key={idx}
                className={`relative flex flex-col items-center group flex-shrink-0 w-64 ${
                  isActive ? "ring-4 ring-[#fcc250] rounded-lg" : ""
                }`}
              >
                <img
                  src={`templets/${img}`}
                  alt={img}
                  className="w-64 h-80 rounded-lg shadow-lg object-cover"
                />
                <div className="absolute bottom-4 w-full flex justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                  <button className="bg-[#fcc250] text-[#29354d] font-bold rounded-full px-4 py-2 shadow hover:bg-[#ffda80]">
                    View Template
                  </button>
                </div>
              </div>
            );
          })}
        </motion.div>
      </div>

      {/* Indicators */}
      <div className="mt-6 flex justify-center space-x-2">
        {templates.map((_, idx) => {
          const isActive = idx === activeIndex % templates.length;
          return (
            <div
              key={idx}
              className={`w-3 h-3 rounded-full ${
                isActive ? "bg-[#fcc250]" : "bg-gray-300"
              }`}
            />
          );
        })}
      </div>

      {/* View More Templates Button */}
      <div className="mt-8 flex justify-center">
        <a
          href="/templates"
          className="bg-[#fcc250] hover:bg-[#ffda80] text-[#29354d] font-bold rounded-full px-8 py-3 shadow-lg transition"
        >
          View More Templates
        </a>
      </div>
    </section>
  );
}
