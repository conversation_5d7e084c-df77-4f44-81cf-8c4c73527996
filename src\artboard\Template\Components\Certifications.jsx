import React from "react";

const Certifications = ({
  certifications = [],
  title = "Certifications",
  sectionStyle = "",
  titleStyle = "",
  itemTitleStyle = "",
  issuerStyle = "",
  dateStyle = "",
  summaryStyle = "",
}) => {
  if (!certifications.length) return null;

  return (
    <section className={sectionStyle}>
      <h3 className={`text-lg font-bold mb-2 ${titleStyle}`}>{title}</h3>
      {certifications.map((cert, index) => (
        <div key={index} className="mb-4">
          <div className="flex justify-between">
            <strong className={`text-base ${itemTitleStyle}`}>
              {cert.name}
            </strong>
            <span className={`text-sm italic ${dateStyle}`}>{cert.date}</span>
          </div>
          <div className={`text-sm italic ${issuerStyle}`}>
            {cert.issuer}
          </div>
          {cert.summary && (
            <div
              className={`text-sm mt-1 ${summaryStyle}`}
              dangerouslySetInnerHTML={{ __html: cert.summary }}
            />
          )}
        </div>
      ))}
    </section>
  );
};

export default Certifications;
