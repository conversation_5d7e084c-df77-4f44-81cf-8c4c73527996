import React, { useEffect } from "react";
import { useAuthStore } from "../../store/authStore";
import { useResumeStore } from "../../store/useResumeDetailStore";
import { useThemeStore } from "../../store/themeStore";
import { useTypographyStore } from "../../store/themeTypographyStore";

const SleekLeftTimelineTemplate = ({
  exportMode = false,
  themeOverrides = {},
}) => {
  const { user, fetchCurrentUser } = useAuthStore();
  const { resume, fetchResume } = useResumeStore();

  const { primaryColor, backgroundColor, textColor } = useThemeStore();
  const { fontFamily, fontSize, lineHeight, fontVariant } =
    useTypographyStore();

  const variantStyle = {};
  if (fontVariant === "italic") variantStyle.fontStyle = "italic";
  else if (!isNaN(fontVariant)) variantStyle.fontWeight = fontVariant;

  useEffect(() => {
    if (!user) fetchCurrentUser();
  }, []);

  useEffect(() => {
    if (user?.resumeId && !resume) fetchResume(user.resumeId);
  }, [user]);

  if (!resume || !user) return exportMode ? null : <p>Loading resume...</p>;

  const {
    Experience = [],
    Education = [],
    Skills = [],
    Languages = [],
    Projects = [],
    summery,
  } = resume;

  return (
    <div
      className="a4-container px-8 py-6 grid grid-cols-3 gap-6"
      style={{
        backgroundColor: exportMode ? "#fff" : backgroundColor,
        color: textColor,
        fontFamily,
        fontSize,
        lineHeight,
        ...variantStyle,
      }}
    >
      {/* Left Column Timeline */}
      <div className="col-span-1 pr-6 border-r-2 border-gray-300">
        <h2 className="text-lg font-bold mb-4" style={{ color: primaryColor }}>
          Experience Timeline
        </h2>
        <ul className="space-y-4 border-l-2 border-gray-400 pl-4">
          {Experience.map((exp, idx) => (
            <li key={idx} className="relative">
              <span
                className="absolute -left-[11px] top-1 w-4 h-4 rounded-full"
                style={{ backgroundColor: primaryColor }}
              ></span>
              <p className="text-sm font-semibold">
                {exp.Position} @ {exp.Company}
              </p>
              <p className="text-xs italic text-gray-600">
                {formatDate(exp.StartDate)} - {formatDate(exp.EndDate)}
              </p>
              <p className="text-xs mt-1">{exp.Description}</p>
            </li>
          ))}
        </ul>
      </div>

      {/* Main Content */}
      <div className="col-span-2">
        <section className="mb-4">
          <h1 className="text-2xl font-bold" style={{ color: primaryColor }}>
            {user.name}
          </h1>
          {resume.Headline && (
            <p className="italic text-sm text-gray-600">{resume.Headline}</p>
          )}
        </section>

        {summery && (
          <section className="mb-4">
            <h2 className="text-md font-bold" style={{ color: primaryColor }}>
              Summary
            </h2>
            <p className="text-sm mt-1">{summery}</p>
          </section>
        )}

        <section className="mb-4">
          <h2 className="text-md font-bold" style={{ color: primaryColor }}>
            Education
          </h2>
          <ul className="space-y-2 mt-2">
            {Education.map((edu, idx) => (
              <li key={idx}>
                <p className="text-sm font-semibold">
                  {edu.Degree} - {edu.Institution}
                </p>
                <p className="text-xs italic text-gray-600">
                  {formatDate(edu.StartDate)} - {formatDate(edu.EndDate)}
                </p>
                <p className="text-xs">{edu.Description}</p>
              </li>
            ))}
          </ul>
        </section>

        <section className="mb-4">
          <h2 className="text-md font-bold" style={{ color: primaryColor }}>
            Projects
          </h2>
          <ul className="space-y-2 mt-2">
            {Projects.map((proj, idx) => (
              <li key={idx}>
                <p className="text-sm font-semibold">{proj.Title}</p>
                <p className="text-xs italic text-gray-600">
                  {proj.Technologies?.join(", ")}
                </p>
                <p className="text-xs">{proj.Description}</p>
              </li>
            ))}
          </ul>
        </section>

        <section className="grid grid-cols-2 gap-4">
          <div>
            <h2 className="text-md font-bold" style={{ color: primaryColor }}>
              Skills
            </h2>
            <ul className="list-disc pl-4 text-xs mt-2">
              {Skills.map((s, idx) => (
                <li key={idx}>{s.Skill}</li>
              ))}
            </ul>
          </div>
          <div>
            <h2 className="text-md font-bold" style={{ color: primaryColor }}>
              Languages
            </h2>
            <ul className="list-disc pl-4 text-xs mt-2">
              {Languages.map((l, idx) => (
                <li key={idx}>
                  {l.Name} - {l.Proficiency}
                </li>
              ))}
            </ul>
          </div>
        </section>
      </div>
    </div>
  );
};

const formatDate = (date) => {
  if (!date) return "Present";
  try {
    return new Date(date).toLocaleDateString("en-IN", {
      month: "short",
      year: "numeric",
    });
  } catch {
    return "N/A";
  }
};

export default SleekLeftTimelineTemplate;
