import React, { createContext, useContext, useEffect, useState } from "react";
import { useResumeStore } from "../store/useResumeDetailStore";

const ResumeContext = createContext();

export const ResumeProvider = ({ resumeId, children }) => {
  const { resume, fetchResume, loading } = useResumeStore();
  const [error, setError] = useState(null);

  useEffect(() => {
    if (resumeId) {
      fetchResume(resumeId).catch(setError);
    }
  }, [resumeId]);

  return (
    <ResumeContext.Provider value={{ resume, loading, error }}>
      {children}
    </ResumeContext.Provider>
  );
};

export const useResume = () => {
  const context = useContext(ResumeContext);
  if (!context) {
    throw new Error("useResume must be used within a ResumeProvider");
  }
  return context;
};
