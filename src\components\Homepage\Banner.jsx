import React from "react";
import { motion } from "framer-motion";

const templates = [
  "banner/templet1.webp",
  "banner/templet2.webp",
  "banner/templet3.webp",
];

export default function Banner() {
  const images = [...templates, ...templates];
  return (
    <section className="relative flex flex-col md:flex-row items-center justify-center bg-gradient-to-r from-[#f5f7fb] via-[#f5f7fb] to-white rounded-br-[56px] overflow-hidden py-12 md:py-20 px-6 md:px-16">
      <div className="z-10 flex-1 max-w-xl flex flex-col items-start text-left space-y-5">
        <div className="mb-2">
          <svg
            width="32"
            height="32"
            fill="none"
            className="text-[#fcc250]"
            viewBox="0 0 32 32"
          >
            <g>
              <circle cx="16" cy="6" r="2" fill="currentColor" />
              <circle cx="16" cy="26" r="2" fill="currentColor" />
              <circle cx="6" cy="16" r="2" fill="currentColor" />
              <circle cx="26" cy="16" r="2" fill="currentColor" />
              <circle cx="22" cy="10" r="1" fill="currentColor" />
              <circle cx="10" cy="22" r="1" fill="currentColor" />
              <circle cx="22" cy="22" r="1" fill="currentColor" />
              <circle cx="10" cy="10" r="1" fill="currentColor" />
            </g>
          </svg>
        </div>
        <h1 className="text-4xl md:text-6xl font-bold text-[#29354d] leading-tight">
          The CV that gets
          <br />
          the job... <span className="text-[#fcc250]">done</span>
        </h1>
        <p className="text-lg md:text-xl text-[#29354d]">
          Build a new CV or improve your existing one
          <br className="hidden md:block" />
          with step-by-step expert guidance.
        </p>
        <div className="flex flex-wrap items-center gap-4 mt-4">
          <button className="bg-[#fcc250] hover:bg-[#e1af46] text-[#29354d] text-lg font-semibold rounded-full px-8 py-3 shadow transition">
            Create your CV
          </button>
          <button className="border-2 border-[#29354d] bg-white text-[#29354d] text-lg font-semibold rounded-full px-8 py-3 hover:bg-gray-100 transition">
            Upgrade a CV
          </button>
          <span className="bg-[#ffe7c2] text-[#4d2600] rounded-full px-4 py-1 text-base font-medium">
            🪄 AI-powered
          </span>
        </div>
      </div>

      <div className="relative flex justify-center items-center mt-12 md:mt-0 flex-1">
        <div className="relative w-[410px] h-[340px] rounded-xl overflow-hidden z-10">
          <motion.div
            className="flex h-full"
            animate={{ x: ["0%", "-50%"] }}
            transition={{
              repeat: Infinity,
              repeatType: "loop",
              duration: 12,
              ease: "linear",
            }}
          >
            {images.map((src, idx) => (
              <img
                key={idx}
                src={src}
                alt={`Resume Template ${(idx % 3) + 1}`}
                className="w-[195px] h-[290px] mr-9 rounded-xl shadow-lg bg-white object-cover"
                draggable={false}
              />
            ))}
          </motion.div>
        </div>
      </div>

      <div className="hidden md:block absolute right-0 top-0 h-full w-[300px] z-0">
        <img
          src="https://images.unsplash.com/photo-1579389083395-4507e98b5e67?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0"
          alt="Right Decorative"
          className="w-full h-full object-cover rounded-bl-[40px]"
        />
      </div>
    </section>
  );
}
