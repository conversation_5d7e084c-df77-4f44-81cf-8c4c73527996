import React from "react";

const Profiles = ({
  profiles = [],
  title = "Profiles",
  sectionStyle = "",
  titleStyle = "",
  listStyle = "",
  itemStyle = "",
  linkStyle = "",
  iconStyle = "",
}) => {
  if (!profiles || profiles.length === 0) return null;

  return (
    <section className={sectionStyle}>
      {title && (
        <h3 className={`text-lg font-bold mb-2 ${titleStyle}`}>
          {title}
        </h3>
      )}
      <ul className={`list-none ${listStyle}`}>
        {profiles.map((profile, idx) => (
          <li key={idx} className={itemStyle}>
            <a
              href={profile.url}
              target="_blank"
              rel="noopener noreferrer"
              className={`flex items-center gap-2 hover:underline ${linkStyle}`}
            >
              {profile.icon && (
                <span className={iconStyle}>{profile.icon}</span>
              )}
              <span>{profile.name}</span>
            </a>
          </li>
        ))}
      </ul>
    </section>
  );
};

export default Profiles;
