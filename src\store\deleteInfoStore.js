import { create } from "zustand";
import axios from "axios";
import { useResumeStore } from "./useResumeDetailStore";

// const API_BASE = "http://localhost:5000/api/resume/deleteinfo";
const API_BASE = "https://resumebuilder-m27v.onrender.com/api/resume/deleteinfo";


export const useDeleteInfoStore = create((set) => ({
    loading: false,
    error: null,
    success: null,

    deleteInfo: async ({ resumeId, section, entryId }) => {
        set({ loading: true, error: null, success: null });

        try {
            const token = localStorage.getItem("token");

            await axios.delete(
                `${API_BASE}/${resumeId}/delete${section.toLowerCase()}/${entryId}`,
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                    withCredentials: true,
                }
            );

            // ✅ Force refresh after deletion
            await useResumeStore.getState().fetchResume(resumeId, true);

            set({
                loading: false,
                success: `${section} deleted successfully`,
            });
            return true;
        } catch (err) {
            set({
                loading: false,
                error:
                    err?.response?.data?.error ||
                    `Failed to delete ${section.toLowerCase()}`,
            });
            return false;
        }
    },

    resetStatus: () => {
        set({ loading: false, error: null, success: null });
    },
}));
