import React, { useEffect, useState } from "react";
import { FaGraduationCap, FaBars, FaPlus, FaTimes } from "react-icons/fa";
import DataCard from "../../Components/DataCard";
import { format } from "date-fns";

import { useResumeStore } from "../../../store/useResumeDetailStore";
import { useAddInfoStore } from "../../../store/addInfoStore";
import { useDeleteInfoStore } from "../../../store/deleteInfoStore";
import { useUpdateInfoStore } from "../../../store/updateInfoStore";

import EducationModal from "../../Components/EducationModal";

const Education = () => {
  const { resume, fetchResume } = useResumeStore();
  const { addInfo, resetStatus: resetAddStatus } = useAddInfoStore();
  const { deleteInfo } = useDeleteInfoStore();
  const { updateInfo, resetStatus: resetUpdateStatus } = useUpdateInfoStore();

  const [modalOpen, setModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(null);

  const resumeId = resume?._id;
  const education = resume?.Education || [];

  useEffect(() => {
    if (resumeId) fetchResume(resumeId);
  }, [resumeId]);

  const formatDateRange = (start, end) => {
    const s = start ? format(new Date(start), "MMM yyyy") : "";
    const e = end ? format(new Date(end), "MMM yyyy") : "Present";
    return `${s} - ${e}`;
  };

  const handleCreateOrUpdate = async (data, entryId = null) => {
    if (!resumeId) return;

    if (entryId) {
      await updateInfo({
        resumeId,
        section: "education",
        entryId,
        updatedData: data,
      });
    } else {
      await addInfo({ resumeId, section: "education", newData: data });
    }

    await fetchResume(resumeId);
    setModalOpen(false);
    setEditData(null);
  };

  const confirmDeleteEducation = async () => {
    if (!resumeId || !confirmDelete) return;

    try {
      await deleteInfo({
        resumeId,
        section: "education",
        entryId: confirmDelete._id,
      });
      await fetchResume(resumeId);
    } catch (err) {
      console.error("Failed to delete:", err);
    } finally {
      setConfirmDelete(null);
    }
  };

  return (
    <div className="bg-white min-h-screen py-10 px-4 md:px-8 text-black font-sans">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div className="flex items-center gap-3">
          <FaGraduationCap className="text-black text-2xl" />
          <h2 className="text-2xl font-bold">Education</h2>
        </div>
        <FaBars className="text-black text-xl" />
      </div>

      {/* Add Button */}
      <div
        onClick={() => {
          resetAddStatus();
          resetUpdateStatus();
          setEditData(null);
          setModalOpen(true);
        }}
        className="border-2 border-dashed border-gray-400 rounded-lg py-5 px-8 flex items-center justify-center bg-gray-200 hover:bg-gray-300 text-black text-lg font-medium mb-8 cursor-pointer transition-all"
      >
        <FaPlus className="mr-2" />
        Add a new education
      </div>

      {/* Education Cards */}
      <div className="flex flex-wrap gap-4">
        {education.length === 0 ? (
          <p className="text-gray-500 text-center w-full">
            No education added yet.
          </p>
        ) : (
          education.map((edu) => (
            <div className="w-fit min-w-[250px]" key={edu._id}>
              <DataCard
                title={`${edu.Institution} (${edu.Degree})`}
                subtitle={`${edu.Location} • ${formatDateRange(
                  edu.StartDate,
                  edu.EndDate
                )}`}
                description={edu.Summary}
                onDelete={() => setConfirmDelete(edu)}
                onEdit={() => {
                  setEditData(edu);
                  setModalOpen(true);
                }}
                onCopy={() => {
                  const text = `${edu.Institution} - ${edu.Degree}\n${
                    edu.Location
                  }, ${formatDateRange(edu.StartDate, edu.EndDate)}\n${
                    edu.Summary || ""
                  }`;
                  navigator.clipboard.writeText(text);
                  alert("Copied to clipboard!");
                }}
              />
            </div>
          ))
        )}
      </div>

      {/* Modal */}
      {modalOpen && (
        <EducationModal
          initial={editData}
          onClose={() => {
            setModalOpen(false);
            setEditData(null);
          }}
          onSubmit={handleCreateOrUpdate}
        />
      )}

      {/* Delete Confirmation Modal */}
      {confirmDelete && (
        <div className="fixed inset-0 z-50 bg-black/60 flex items-center justify-center px-4">
          <div className="bg-white max-w-md w-full p-6 rounded-lg shadow-xl text-[#29354d]">
            <h3 className="text-lg font-bold mb-2">Delete Education Entry</h3>
            <p className="text-sm mb-4">
              Are you sure you want to delete{" "}
              <strong>{confirmDelete.Institution}</strong>?
            </p>

            <div className="border p-3 rounded bg-gray-50 text-sm mb-4">
              <p>
                <strong>Degree:</strong> {confirmDelete.Degree}
              </p>
              <p>
                <strong>Location:</strong> {confirmDelete.Location}
              </p>
              <p>
                <strong>Duration:</strong>{" "}
                {formatDateRange(
                  confirmDelete.StartDate,
                  confirmDelete.EndDate
                )}
              </p>
              {confirmDelete.Summary && (
                <p className="mt-2">
                  <strong>Summary:</strong> {confirmDelete.Summary}
                </p>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setConfirmDelete(null)}
                className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-sm"
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteEducation}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 text-sm"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Education;
