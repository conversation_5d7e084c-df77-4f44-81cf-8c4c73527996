import { create } from 'zustand';
import axios from 'axios';
import { toast } from 'react-hot-toast';
import { API_BASE_URL } from '../lib/constants';

const RESUME_ENDPOINT = `${API_BASE_URL}/resume`;
const CREATE_RESUME_ROUTE = `${API_BASE_URL}/resumeroutes`;
const ADD_INFO_ROUTE = `${API_BASE_URL}/addinforesume`;

export const useResumeStore = create((set, get) => ({
    resumes: [],
    selectedResume: null,
    isLoading: false,
    error: null,
    fetchAllResumes: async () => {
        set({ isLoading: true });
        try {
            const res = await axios.get(RESUME_ENDPOINT, { withCredentials: true });
            set({ resumes: res.data, isLoading: false });
        } catch (err) {
            const msg = err.response?.data?.message || 'Failed to fetch resumes';
            set({ error: msg, isLoading: false });
            toast.error(msg);
        }
    },

    // Fetch single resume
    fetchResumeById: async (id) => {
        try {
            const res = await axios.get(`${RESUME_ENDPOINT}/${id}`, { withCredentials: true });
            set({ selectedResume: res.data });
            return res.data;
        } catch {
            toast.error('Failed to load resume');
        }
    },

    // Create resume
    createResume: async (title) => {
        try {
            const res = await axios.post(`${CREATE_RESUME_ROUTE}/create`, { Title: title }, { withCredentials: true });
            toast.success('Resume created');
            get().fetchAllResumes();
            return res.data.resume;
        } catch (err) {
            toast.error(err.response?.data?.message || 'Failed to create resume');
        }
    },

    // Delete resume
    deleteResume: async (id) => {
        try {
            await axios.delete(`${RESUME_ENDPOINT}/${id}`, { withCredentials: true });
            toast.success('Resume deleted');
            get().fetchAllResumes();
        } catch {
            toast.error('Failed to delete resume');
        }
    },

    // Update title
    updateResumeTitle: async (resumeId, newTitle) => {
        try {
            const res = await axios.patch(`${CREATE_RESUME_ROUTE}/updatetitle`, { resumeId, newTitle }, { withCredentials: true });
            toast.success('Title updated');
            get().fetchAllResumes();
            return res.data.resume;
        } catch (err) {
            toast.error(err.response?.data?.message || 'Failed to update title');
        }
    },

    // Update template
    updateTemplate: async (resumeId, templateName) => {
        try {
            const res = await axios.patch(`${CREATE_RESUME_ROUTE}/updatetemplate`, { resumeId, templateName }, { withCredentials: true });
            toast.success('Template updated');
            get().fetchAllResumes();
            return res.data.resume;
        } catch {
            toast.error('Failed to update template');
        }
    },

    // Toggle publication
    togglePublication: async (resumeId, status) => {
        try {
            const res = await axios.patch(`${CREATE_RESUME_ROUTE}/updatepublication`, { resumeId, status }, { withCredentials: true });
            toast.success(`Resume ${status ? 'published' : 'unpublished'}`);
            get().fetchAllResumes();
            return res.data.resume;
        } catch {
            toast.error('Failed to change publication status');
        }
    },

    // Get publish URL
    getPublishURL: async (resumeId) => {
        try {
            const res = await axios.get(`${CREATE_RESUME_ROUTE}/publish-url/${resumeId}`, { withCredentials: true });
            return res.data.url;
        } catch {
            toast.error('Failed to get publish URL');
        }
    },

    // Toggle public access
    togglePublicAccess: async (resumeId, status) => {
        try {
            const res = await axios.patch(`${CREATE_RESUME_ROUTE}/updatepublicaccess`, { resumeId, status }, { withCredentials: true });
            toast.success(`Public access ${status ? 'enabled' : 'disabled'}`);
            get().fetchAllResumes();
            return res.data.resume;
        } catch {
            toast.error('Failed to change public access');
        }
    },
    updatePDFLink: async (resumeId, pdfLink) => {
        try {
            const res = await axios.patch(`${CREATE_RESUME_ROUTE}/updatepdf-link`, { resumeId, pdfLink }, { withCredentials: true });
            toast.success('PDF link updated');
            get().fetchAllResumes();
            return res.data.resume;
        } catch {
            toast.error('Failed to update PDF link');
        }
    },

    // Duplicate resume
    duplicateResume: async (id) => {
        try {
            const res = await axios.post(`${RESUME_ENDPOINT}/${id}/duplicate`, {}, { withCredentials: true });
            toast.success('Resume duplicated');
            get().fetchAllResumes();
            return res.data.resume;
        } catch {
            toast.error('Failed to duplicate resume');
        }
    },

    // Add to section (generic)
    addToResumeSection: async (section, resumeId, data) => {
        try {
            const res = await axios.post(`${ADD_INFO_ROUTE}/add${section}/${resumeId}`, data, { withCredentials: true });
            toast.success(`${section} added`);
            get().fetchResumeById(resumeId);
            return res.data;
        } catch (err) {
            toast.error(`Failed to add ${section}`);
            console.error(err);
        }
    },

    addProfile: async (id, data) => get().addToResumeSection('profile', id, data),
    addExperience: async (id, data) => get().addToResumeSection('experience', id, data),
    addEducation: async (id, data) => get().addToResumeSection('education', id, data),
    addSkill: async (id, data) => get().addToResumeSection('skill', id, data),
    addLanguage: async (id, data) => get().addToResumeSection('language', id, data),
    addAward: async (id, data) => get().addToResumeSection('award', id, data),
    addCertification: async (id, data) => get().addToResumeSection('certification', id, data),
    addInterest: async (id, data) => get().addToResumeSection('interest', id, data),
    addPublication: async (id, data) => get().addToResumeSection('publication', id, data),
    addVolunteering: async (id, data) => get().addToResumeSection('volunteering', id, data),
    addReference: async (id, data) => get().addToResumeSection('reference', id, data),
}));
