import React from "react";
import ResumeThumb from "../../../public/templets/template1.png"; // ✅ make sure this path is correct or use /templets/template1.png

const ResumeCard = ({ resume, onDelete, onPreview }) => {
    return (
        <div className="relative bg-white rounded-lg shadow-lg overflow-hidden group transition-transform duration-300 hover:scale-[1.02]">
            {/* Thumbnail */}
            <div
                className="w-full h-48 bg-cover bg-center bg-no-repeat"
                style={{ backgroundImage: `url(${ResumeThumb})` }} // ✅ fixed
            />

            {/* Content */}
            <div className="p-4">
                <h3 className="text-lg font-semibold text-[#29354d]">
                    {resume.Title}
                </h3>
                <p className="text-sm text-gray-500">{resume.Email}</p>
                <p className="text-xs mt-1 text-gray-400">
                    Created: {new Date(resume.createdAt).toLocaleDateString()}
                </p>

                <div className="flex justify-between mt-4">
                    <button
                        onClick={() => onPreview(resume)}
                        className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                    >
                        Preview
                    </button>

                    <button
                        onClick={() => onDelete(resume._id)}
                        className="px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600"
                    >
                        Delete
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ResumeCard;
