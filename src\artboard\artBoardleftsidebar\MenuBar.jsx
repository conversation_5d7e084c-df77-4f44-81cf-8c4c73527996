import React, { useRef, useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useAuthStore } from "../../store/authStore";
import { ResumeProvider } from "../../context/ResumeContext";

import {
  FaUser,
  FaRegCreditCard,
  FaShareAlt,
  FaBriefcase,
  FaGraduationCap,
  FaRunning,
  FaLanguage,
  FaKey,
  FaComments,
  FaRProject ,
  FaBook,
  FaUserFriends,
  FaAddressCard,
  FaThLarge,
  FaSignOutAlt,
  FaCog,
  FaAward,
} from "react-icons/fa";

// Section Components
import Basics from "./components/Basics";
import Summary from "./components/Summary";
import Profiles from "./components/Profiles";
import Experience from "./components/Experience";
import Education from "./components/Education";
import Skills from "./components/Skills";
import Languages from "./components/Languages";
import Awards from "./components/Awards";
import Certifications from "./components/Certifications";
import Projects from "./components/Projects";
import Publications from "./components/Publications";
import Volunteering from "./components/Volunteering";
import References from "./components/References";

const menuItems = [
  { icon: <FaUser />, label: "Profile" },
  { icon: <FaRegCreditCard />, label: "summary" },
  { icon: <FaShareAlt />, label: "profile" },
  { icon: <FaBriefcase />, label: "Experience" },
  { icon: <FaGraduationCap />, label: "Education" },
  { icon: <FaRunning />, label: "Skills" },
  { icon: <FaLanguage />, label: "Languages" },
  { icon: <FaAward />, label: "awards" },
  { icon: <FaComments />, label: "certification" },
  { icon: <FaRProject  />, label: "Projects" },
  { icon: <FaBook />, label: "Docs" },
  { icon: <FaUserFriends />, label: "Contacts" },
  { icon: <FaAddressCard />, label: "References" },
];

const MenuBar = ({ onNavigate, activeIdx, setActiveIdx }) => {
  const { user, logout } = useAuthStore();
  const [showMenu, setShowMenu] = useState(false);
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate("/login");
  };

  return (
    <div className="w-14 bg-[#020202] text-white h-screen fixed left-0 top-0 flex flex-col justify-between z-50 shadow-lg">
      <div className="flex-1 overflow-y-auto flex flex-col items-center pt-6 pb-6 gap-6">
        {menuItems.map((item, idx) => (
          <button
            key={item.label}
            onClick={() => {
              onNavigate(item.label);
              setActiveIdx(idx);
            }}
            title={item.label}
            className={`w-10 h-10 flex items-center justify-center rounded-full transition-all duration-200 ${
              idx === activeIdx
                ? "bg-[#23262b] border-2 border-[#f7f5f2]"
                : "hover:bg-[#39445d]"
            }`}
          >
            {item.icon}
          </button>
        ))}
      </div>

      <div className="relative flex flex-col items-center gap-4 pb-4">
        <button
          onClick={() => setShowMenu(!showMenu)}
          title="User Menu"
          className="w-10 h-10 rounded-full bg-[#fcc250] flex items-center justify-center overflow-hidden hover:scale-105 transition"
        >
          <img
            src="https://lh3.googleusercontent.com/a/default-user"
            alt="User"
            className="w-7 h-7 rounded-full object-cover"
          />
        </button>

        {showMenu && (
          <>
            <div
              onClick={() => setShowMenu(false)}
              className="fixed inset-0 z-40 bg-black/30 backdrop-blur-sm"
            />
            <div className="absolute bottom-14 left-14 bg-white text-black rounded shadow-lg w-44 py-2 z-50 animate-fade-in">
              <button
                onClick={() => {
                  navigate("/dashboard");
                  setShowMenu(false);
                }}
                className="flex items-center gap-2 px-4 py-2 hover:bg-gray-100 w-full"
              >
                <FaThLarge /> Dashboard
              </button>
              {user?.role === "admin" && (
                <button
                  onClick={() => {
                    navigate("/dashboard/settings");
                    setShowMenu(false);
                  }}
                  className="flex items-center gap-2 px-4 py-2 hover:bg-gray-100 w-full"
                >
                  <FaCog /> Settings
                </button>
              )}
              <button
                onClick={handleLogout}
                className="flex items-center gap-2 px-4 py-2 hover:bg-gray-100 w-full text-red-500"
              >
                <FaSignOutAlt /> Sign out
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

const MenuLayoutContent = ({
  handleNavigate,
  sectionRefs,
  activeIdx,
  setActiveIdx,
}) => {
  return (
    <div className="flex">
      <MenuBar
        onNavigate={handleNavigate}
        activeIdx={activeIdx}
        setActiveIdx={setActiveIdx}
      />
      <main className="ml-14 w-full bg-[#f5f6fa] min-h-screen">
        <div className="max-w-5xl mx-auto px-4 py-10 space-y-16">
          <div ref={sectionRefs.Profile}>
            <Basics />
          </div>
          <div ref={sectionRefs.summary}>
            <Summary />
          </div>
          <div ref={sectionRefs.profile}>
            <Profiles />
          </div>
          <div ref={sectionRefs.Experience}>
            <Experience />
          </div>
          <div ref={sectionRefs.Education}>
            <Education />
          </div>
          <div ref={sectionRefs.Skills}>
            <Skills />
          </div>
          <div ref={sectionRefs.Languages}>
            <Languages />
          </div>
          <div ref={sectionRefs.awards}>
            <Awards />
          </div>
          <div ref={sectionRefs.certification}>
            <Certifications />
          </div>
          <div ref={sectionRefs.Projects}>
            <Projects />
          </div>
          <div ref={sectionRefs.Docs}>
            <Publications />
          </div>
          <div ref={sectionRefs.Contacts}>
            <Volunteering />
          </div>
          <div ref={sectionRefs.References}>
            <References />
          </div>
        </div>
      </main>
    </div>
  );
};

const MenuLayout = () => {
  const { resumeId } = useParams();
  const sectionRefs = Object.fromEntries(
    menuItems.map(({ label }) => [label, useRef()])
  );
  const [activeIdx, setActiveIdx] = useState(0);

  const handleNavigate = (label) => {
    const ref = sectionRefs[label];
    if (ref?.current) {
      ref.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  // Track scrolling position to update active menu
  useEffect(() => {
    const handleScroll = () => {
      const offsets = menuItems.map(({ label }, idx) => {
        const el = sectionRefs[label]?.current;
        if (!el) return { idx, top: Infinity };
        const rect = el.getBoundingClientRect();
        return { idx, top: Math.abs(rect.top) };
      });
      const closest = offsets.reduce((a, b) => (a.top < b.top ? a : b));
      setActiveIdx(closest.idx);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <ResumeProvider resumeId={resumeId}>
      <MenuLayoutContent
        handleNavigate={handleNavigate}
        sectionRefs={sectionRefs}
        activeIdx={activeIdx}
        setActiveIdx={setActiveIdx}
      />
    </ResumeProvider>
  );
};

export default MenuLayout;
